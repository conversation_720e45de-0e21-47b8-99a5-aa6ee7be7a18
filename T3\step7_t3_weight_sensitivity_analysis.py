"""
第三题 步骤7：T3企业双目标权重敏感性分析
测试不同的收益-客户流失率权重组合，找到最优权重配置
"""
import pandas as pd
import numpy as np
from deap import base, creator, tools
import random
import matplotlib.pyplot as plt
from scipy.interpolate import interp1d
import warnings
warnings.filterwarnings('ignore')

print("="*80)
print("第三题 步骤7：T3企业双目标权重敏感性分析")
print("测试不同权重组合：收益最大化 vs 客户流失率最小化")
print("="*80)

class T3WeightSensitivityAnalyzer:
    def __init__(self):
        # 基本约束
        self.total_budget = 1e8  # 1亿元
        self.min_loan = 1e5      # 10万元
        self.max_loan = 1e6      # 100万元
        self.min_rate = 0.04     # 4%
        self.max_rate = 0.15     # 15%
        
        # 权重组合测试
        self.weight_combinations = [
            (0.7, 0.3),  # 偏收益
            (0.6, 0.4),  # 均衡偏收益
            (0.5, 0.5),  # 完全均衡
            (0.4, 0.6),  # 均衡偏客户
            (0.3, 0.7),  # 偏客户保留
            (0.2, 0.8)   # 极度重视客户保留
        ]
        
        # 载入数据
        self.load_data()
        self.setup_interpolation()
        
    def load_data(self):
        """加载数据"""
        print("读取T3企业AHP评估结果...")
        self.t3_data = pd.read_csv('T3企业_AHP综合评估结果.csv')
        print(f"T3企业数据: {len(self.t3_data)}家企业")
        
        # 读取利率-流失率关系
        rate_data = pd.read_csv('4.csv')
        self.rates = []
        self.loss_rates_A = []
        self.loss_rates_B = []
        self.loss_rates_C = []
        
        for i in range(2, len(rate_data)):
            try:
                rate = float(rate_data.iloc[i, 0])
                loss_A = float(rate_data.iloc[i, 1])
                loss_B = float(rate_data.iloc[i, 2])
                loss_C = float(rate_data.iloc[i, 3])
                
                self.rates.append(rate)
                self.loss_rates_A.append(loss_A)
                self.loss_rates_B.append(loss_B)
                self.loss_rates_C.append(loss_C)
            except:
                continue
                
    def setup_interpolation(self):
        """设置插值函数"""
        self.interp_A = interp1d(self.rates, self.loss_rates_A, kind='linear', 
                                bounds_error=False, fill_value='extrapolate')
        self.interp_B = interp1d(self.rates, self.loss_rates_B, kind='linear', 
                                bounds_error=False, fill_value='extrapolate')
        self.interp_C = interp1d(self.rates, self.loss_rates_C, kind='linear', 
                                bounds_error=False, fill_value='extrapolate')
    
    def get_customer_loss_rate(self, interest_rate, credit_rating):
        """获取客户流失率"""
        if credit_rating == 'A':
            return max(0, min(1, self.interp_A(interest_rate)))
        elif credit_rating == 'B':
            return max(0, min(1, self.interp_B(interest_rate)))
        elif credit_rating == 'C':
            return max(0, min(1, self.interp_C(interest_rate)))
        else:
            return max(0, min(1, self.interp_B(interest_rate)))
    
    def evaluate_solution(self, solution, revenue_weight, loss_weight):
        """评估解（根据权重）"""
        n_enterprises = len(self.t3_data)
        
        loans = solution[::2][:n_enterprises]
        rates = solution[1::2][:n_enterprises]
        
        total_loan = sum(loans)
        
        if total_loan > self.total_budget:
            return -1e10, 1e10
        
        total_revenue = 0
        weighted_loss_rate = 0
        active_loans = 0
        
        for i, (loan, rate) in enumerate(zip(loans, rates)):
            if loan > 0:
                if loan < self.min_loan or loan > self.max_loan:
                    return -1e10, 1e10
                if rate < self.min_rate or rate > self.max_rate:
                    return -1e10, 1e10
                
                enterprise = self.t3_data.iloc[i]
                credit_rating = enterprise.get('继承信誉评级', 'B')
                
                # 获取客户流失率
                loss_rate = self.get_customer_loss_rate(rate, credit_rating)
                
                # ✅ 修正：使用与T2相同的收益计算公式
                # 期望收益 = 贷款金额 × 利率 × (1 - 流失率)
                expected_revenue = loan * rate * (1 - loss_rate)
                total_revenue += expected_revenue
                
                weighted_loss_rate += loss_rate * loan
                active_loans += loan
        
        if active_loans > 0:
            avg_loss_rate = weighted_loss_rate / active_loans
        else:
            avg_loss_rate = 1.0
        
        # 加权目标函数
        weighted_objective = revenue_weight * total_revenue - loss_weight * avg_loss_rate * 1e6
        
        return weighted_objective, avg_loss_rate
    
    def optimize_with_weights(self, revenue_weight, loss_weight):
        """用指定权重进行优化"""
        print(f"\n🎯 测试权重组合: 收益{revenue_weight:.1f} : 客户保留{loss_weight:.1f}")
        
        # 重新创建DEAP类型（避免冲突）
        if hasattr(creator, "FitnessMax"):
            del creator.FitnessMax
        if hasattr(creator, "Individual"):
            del creator.Individual
            
        creator.create("FitnessMax", base.Fitness, weights=(1.0,))  # 单目标优化
        creator.create("Individual", list, fitness=creator.FitnessMax)
        
        def generate_individual():
            n_enterprises = len(self.t3_data)
            individual = []
            
            # 随机选择企业
            n_selected = random.randint(20, min(80, n_enterprises))
            selected_indices = random.sample(range(n_enterprises), n_selected)
            
            for i in range(n_enterprises):
                if i in selected_indices:
                    loan = random.uniform(self.min_loan, self.max_loan)
                    rate = random.uniform(self.min_rate, self.max_rate)
                else:
                    loan = 0.0
                    rate = self.min_rate
                individual.extend([loan, rate])
            
            # 预算控制
            total_loan = sum(individual[::2])
            if total_loan > self.total_budget:
                factor = self.total_budget / total_loan
                for i in range(0, len(individual), 2):
                    individual[i] *= factor
            
            return creator.Individual(individual)
        
        def mutate_individual(individual):
            n_enterprises = len(self.t3_data)
            
            for i in range(n_enterprises):
                loan_idx = i * 2
                rate_idx = i * 2 + 1
                
                if random.random() < 0.1:
                    if individual[loan_idx] > 0:
                        individual[loan_idx] = max(0, min(self.max_loan,
                            individual[loan_idx] * random.uniform(0.7, 1.3)))
                        individual[rate_idx] = max(self.min_rate, min(self.max_rate,
                            individual[rate_idx] + random.uniform(-0.005, 0.005)))
                    else:
                        if random.random() < 0.05:
                            individual[loan_idx] = random.uniform(self.min_loan, self.max_loan)
                            individual[rate_idx] = random.uniform(self.min_rate, self.max_rate)
            
            total_loan = sum(individual[::2])
            if total_loan > self.total_budget:
                factor = self.total_budget / total_loan
                for i in range(0, len(individual), 2):
                    individual[i] *= factor
            
            return individual,
        
        def evaluate_individual(individual):
            obj_value, loss_rate = self.evaluate_solution(individual, revenue_weight, loss_weight)
            return obj_value,
        
        # 设置遗传算法
        toolbox = base.Toolbox()
        toolbox.register("individual", generate_individual)
        toolbox.register("population", tools.initRepeat, list, toolbox.individual)
        toolbox.register("evaluate", evaluate_individual)
        toolbox.register("mate", tools.cxTwoPoint)
        toolbox.register("mutate", mutate_individual)
        toolbox.register("select", tools.selTournament, tournsize=3)
        
        # 创建种群并进化
        population = toolbox.population(n=50)
        
        # 评估初始种群
        fitnesses = toolbox.map(toolbox.evaluate, population)
        for ind, fit in zip(population, fitnesses):
            ind.fitness.values = fit
        
        # 简化进化过程
        NGEN = 100
        for gen in range(NGEN):
            # 选择
            offspring = toolbox.select(population, len(population))
            offspring = list(map(toolbox.clone, offspring))
            
            # 交叉和变异
            for child1, child2 in zip(offspring[::2], offspring[1::2]):
                if random.random() < 0.5:
                    toolbox.mate(child1, child2)
                    del child1.fitness.values
                    del child2.fitness.values
            
            for mutant in offspring:
                if random.random() < 0.2:
                    toolbox.mutate(mutant)
                    del mutant.fitness.values
            
            # 评估
            invalid_ind = [ind for ind in offspring if not ind.fitness.valid]
            fitnesses = toolbox.map(toolbox.evaluate, invalid_ind)
            for ind, fit in zip(invalid_ind, fitnesses):
                ind.fitness.values = fit
            
            population[:] = offspring
            
            if (gen + 1) % 25 == 0:
                print(f"  第{gen+1}代完成")
        
        # 找到最优解
        best_individual = tools.selBest(population, 1)[0]
        
        # 分析最优解
        return self.analyze_solution(best_individual, revenue_weight, loss_weight)
    
    def analyze_solution(self, solution, revenue_weight, loss_weight):
        """分析解的详细信息"""
        n_enterprises = len(self.t3_data)
        loans = solution[::2][:n_enterprises]
        rates = solution[1::2][:n_enterprises]
        
        total_loan = sum(loans)
        total_revenue = 0
        weighted_loss_rate = 0
        active_loans = 0
        
        active_enterprises = []
        
        for i, (loan, rate) in enumerate(zip(loans, rates)):
            if loan > 0:
                enterprise = self.t3_data.iloc[i]
                credit_rating = enterprise.get('继承信誉评级', 'B')
                
                # 获取客户流失率
                loss_rate = self.get_customer_loss_rate(rate, credit_rating)
                
                # ✅ 修正：使用与T2相同的收益计算公式
                expected_revenue = loan * rate * (1 - loss_rate)
                total_revenue += expected_revenue
                
                weighted_loss_rate += loss_rate * loan
                active_loans += loan
                
                active_enterprises.append({
                    '企业代号': enterprise['企业代号'],
                    '信誉评级': credit_rating,
                    '贷款金额': loan,
                    '利率': rate,
                    '流失率': loss_rate
                })
        
        avg_loss_rate = weighted_loss_rate / active_loans if active_loans > 0 else 0
        
        # 统计各级别企业
        rating_stats = {}
        for rating in ['A', 'B', 'C']:
            rating_enterprises = [e for e in active_enterprises if e['信誉评级'] == rating]
            rating_stats[rating] = {
                '数量': len(rating_enterprises),
                '金额': sum(e['贷款金额'] for e in rating_enterprises),
                '平均利率': np.mean([e['利率'] for e in rating_enterprises]) if rating_enterprises else 0,
                '平均流失率': np.mean([e['流失率'] for e in rating_enterprises]) if rating_enterprises else 0
            }
        
        return {
            '权重组合': f"{revenue_weight:.1f}:{loss_weight:.1f}",
            '总放贷额(万元)': total_loan / 1e4,
            '年收益(万元)': total_revenue / 1e4,
            '收益率(%)': (total_revenue / total_loan * 100) if total_loan > 0 else 0,
            '平均客户流失率(%)': avg_loss_rate * 100,
            '放贷企业数': len(active_enterprises),
            '预算利用率(%)': total_loan / self.total_budget * 100,
            '信誉评级统计': rating_stats,
            '详细企业': active_enterprises
        }
    
    def run_sensitivity_analysis(self):
        """运行完整的敏感性分析"""
        print("开始权重敏感性分析...")
        
        results = []
        detailed_results = []
        
        for revenue_weight, loss_weight in self.weight_combinations:
            result = self.optimize_with_weights(revenue_weight, loss_weight)
            results.append(result)
            detailed_results.append(result)
        
        # 保存结果
        summary_data = []
        for result in results:
            summary_data.append({
                '权重组合(收益:客户保留)': result['权重组合'],
                '总放贷额(万元)': result['总放贷额(万元)'],
                '年收益(万元)': result['年收益(万元)'],
                '收益率(%)': result['收益率(%)'],
                '平均客户流失率(%)': result['平均客户流失率(%)'],
                '放贷企业数': result['放贷企业数'],
                '预算利用率(%)': result['预算利用率(%)'],
                'A级企业数': result['信誉评级统计']['A']['数量'],
                'B级企业数': result['信誉评级统计']['B']['数量'],
                'C级企业数': result['信誉评级统计']['C']['数量']
            })
        
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_csv('T3_权重敏感性分析结果.csv', index=False, encoding='utf-8-sig')
        
        print(f"\n✅ 敏感性分析完成！")
        print("\n📊 不同权重组合结果对比:")
        print(summary_df.to_string(index=False))
        
        # 生成可视化
        self.create_visualization(summary_df)
        
        return results
    
    def create_visualization(self, summary_df):
        """生成可视化图表"""
        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        weights = summary_df['权重组合(收益:客户保留)'].values
        revenues = summary_df['年收益(万元)'].values
        loss_rates = summary_df['平均客户流失率(%)'].values
        profit_rates = summary_df['收益率(%)'].values
        enterprise_counts = summary_df['放贷企业数'].values
        
        # 1. 收益 vs 权重
        ax1.plot(range(len(weights)), revenues, 'bo-', linewidth=2, markersize=8)
        ax1.set_xlabel('权重组合')
        ax1.set_ylabel('年收益(万元)')
        ax1.set_title('不同权重下的年收益变化')
        ax1.set_xticks(range(len(weights)))
        ax1.set_xticklabels(weights, rotation=45)
        ax1.grid(True, alpha=0.3)
        
        # 2. 客户流失率 vs 权重
        ax2.plot(range(len(weights)), loss_rates, 'ro-', linewidth=2, markersize=8)
        ax2.set_xlabel('权重组合')
        ax2.set_ylabel('平均客户流失率(%)')
        ax2.set_title('不同权重下的客户流失率变化')
        ax2.set_xticks(range(len(weights)))
        ax2.set_xticklabels(weights, rotation=45)
        ax2.grid(True, alpha=0.3)
        
        # 3. 收益率 vs 权重
        ax3.plot(range(len(weights)), profit_rates, 'go-', linewidth=2, markersize=8)
        ax3.set_xlabel('权重组合')
        ax3.set_ylabel('收益率(%)')
        ax3.set_title('不同权重下的收益率变化')
        ax3.set_xticks(range(len(weights)))
        ax3.set_xticklabels(weights, rotation=45)
        ax3.grid(True, alpha=0.3)
        
        # 4. 帕累托前沿图：收益 vs 流失率
        ax4.scatter(loss_rates, revenues, c=range(len(weights)), cmap='viridis', s=100)
        for i, weight in enumerate(weights):
            ax4.annotate(weight, (loss_rates[i], revenues[i]), 
                        xytext=(5, 5), textcoords='offset points', fontsize=8)
        ax4.set_xlabel('平均客户流失率(%)')
        ax4.set_ylabel('年收益(万元)')
        ax4.set_title('收益-流失率帕累托前沿')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('T3_权重敏感性分析图.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"\n✅ 可视化图表已保存: T3_权重敏感性分析图.png")

# 主程序执行
if __name__ == "__main__":
    analyzer = T3WeightSensitivityAnalyzer()
    results = analyzer.run_sensitivity_analysis()
    
    print(f"\n" + "="*80)
    print("🎉 T3权重敏感性分析完成！")
    print("📊 结果文件:")
    print("  - T3_权重敏感性分析结果.csv")
    print("  - T3_权重敏感性分析图.png")
    print("="*80)
