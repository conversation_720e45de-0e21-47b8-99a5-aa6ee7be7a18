"""
第三题 步骤5：基于九项指标的层次分析法计算
根据指定的重要性排名构建AHP权重体系并计算综合得分
重要性排名（从高到低）：
1. 抗风险指数 2. 负债水平 3. 经营风险 4. 盈利能力 5. 现金流稳定性 
6. 税负压力 7. 企业规模 8. 公司市场竞争力 9. 盈利预测可靠性
"""
import pandas as pd
import numpy as np

print("="*80)
print("第三题 步骤5：基于九项指标的层次分析法计算")
print("="*80)

# 读取T2企业最终九项指标
nine_indicators = pd.read_csv('T2企业_最终九项指标_标准化_排除D级.csv')
print(f"T2企业九项指标数据: {len(nine_indicators)}家企业")

# 根据重要性排名构建AHP权重体系
# 重要性排名：抗风险指数 > 负债水平 > 经营风险 > 盈利能力 > 现金流稳定性 > 税负压力 > 企业规模 > 公司市场竞争力 > 盈利预测可靠性

def construct_ahp_weights_from_ranking():
    """
    基于重要性排名构建AHP权重
    使用递减权重分配方法
    """
    
    # 指标重要性排名（从最重要到最不重要）
    ranking_order = [
        '抗风险指数',        # 1. 最重要 - 疫情冲击下最关键
        '负债水平',          # 2. 信用风险核心指标
        '经营风险',          # 3. 经营稳定性
        '盈利能力',          # 4. 盈利能力
        '现金流稳定性',      # 5. 现金流管理
        '税负压力',          # 6. 税务风险
        '企业规模',          # 7. 企业实力
        '公司市场竞争力',    # 8. 市场地位
        '盈利预测可靠性'     # 9. 最不重要
    ]
    
    # 使用几何递减序列分配权重
    # 权重比例: 1 : 0.8 : 0.64 : 0.512 : 0.41 : 0.328 : 0.262 : 0.21 : 0.168
    base_weights = []
    decay_factor = 0.8  # 衰减系数
    
    for i in range(len(ranking_order)):
        weight = (decay_factor ** i)
        base_weights.append(weight)
    
    # 标准化权重使总和为1
    total_weight = sum(base_weights)
    normalized_weights = [w / total_weight for w in base_weights]
    
    # 构建权重字典
    ahp_weights = {}
    for i, indicator in enumerate(ranking_order):
        ahp_weights[indicator] = normalized_weights[i]
    
    return ahp_weights, ranking_order

# 构建AHP权重
ahp_weights, importance_ranking = construct_ahp_weights_from_ranking()

print(f"\n✅ 基于重要性排名的AHP权重分配:")
print("重要性排名 | 指标名称 | AHP权重 | 权重占比")
print("-" * 60)
for i, indicator in enumerate(importance_ranking, 1):
    weight = ahp_weights[indicator]
    percentage = weight * 100
    print(f"{i:>8d} | {indicator:>12s} | {weight:>7.4f} | {percentage:>6.2f}%")

# 验证权重总和
total_weight = sum(ahp_weights.values())
print(f"\n权重总和验证: {total_weight:.6f} (应为1.000000)")

# 计算T3企业的AHP综合得分
print(f"\n开始计算T3企业AHP综合得分...")

def calculate_t3_ahp_scores(data, weights):
    """计算T3企业的AHP综合得分"""
    ahp_scores = []
    
    for idx, row in data.iterrows():
        score = 0
        for indicator in weights.keys():
            if indicator in row:
                score += row[indicator] * weights[indicator]
        ahp_scores.append(score)
    
    return ahp_scores

# 计算综合得分
t3_ahp_scores = calculate_t3_ahp_scores(nine_indicators, ahp_weights)

# 创建结果数据框
t3_results = nine_indicators[['企业代号']].copy()
t3_results['T3_AHP综合得分'] = t3_ahp_scores

# 按得分降序排序
t3_results = t3_results.sort_values('T3_AHP综合得分', ascending=False).reset_index(drop=True)
t3_results['T3_AHP排名'] = range(1, len(t3_results) + 1)

print(f"\n✅ T3 AHP综合得分统计:")
print(f"最高分: {t3_results['T3_AHP综合得分'].max():.6f}")
print(f"最低分: {t3_results['T3_AHP综合得分'].min():.6f}")  
print(f"平均分: {t3_results['T3_AHP综合得分'].mean():.6f}")
print(f"标准差: {t3_results['T3_AHP综合得分'].std():.6f}")

# 添加信誉评级信息
try:
    t2_complete = pd.read_csv('../T2/T2企业_最终完整数据.csv')
    t3_results = pd.merge(
        t3_results, 
        t2_complete[['企业代号', '继承信誉评级']], 
        on='企业代号', 
        how='left'
    )
except:
    print("无法读取信誉评级信息")

# 显示排名结果
print(f"\n前20名T3企业AHP综合评估结果:")
print("排名 | 企业代号 | AHP得分  | 信誉评级")
print("-" * 40)
for idx, row in t3_results.head(20).iterrows():
    rating = row.get('继承信誉评级', 'N/A')
    print(f"{row['T3_AHP排名']:>3d} | {row['企业代号']:>6s} | {row['T3_AHP综合得分']:8.6f} | {rating:>6s}")

print(f"\n后20名T3企业AHP综合评估结果:")
print("排名 | 企业代号 | AHP得分  | 信誉评级")
print("-" * 40)
for idx, row in t3_results.tail(20).iterrows():
    rating = row.get('继承信誉评级', 'N/A')
    print(f"{row['T3_AHP排名']:>3d} | {row['企业代号']:>6s} | {row['T3_AHP综合得分']:8.6f} | {rating:>6s}")

# 按信誉评级分组分析
if '继承信誉评级' in t3_results.columns:
    print(f"\n按信誉评级分组的T3 AHP得分分析:")
    rating_analysis = t3_results.groupby('继承信誉评级')['T3_AHP综合得分'].agg(['count', 'mean', 'min', 'max']).round(6)
    rating_analysis.columns = ['企业数量', '平均得分', '最低得分', '最高得分']
    print(rating_analysis)

# 保存T3 AHP评估结果
t3_results.to_csv('T3企业_AHP综合评估结果.csv', index=False, encoding='utf-8-sig')
print(f"\n✅ T3企业AHP综合评估结果已保存至: T3企业_AHP综合评估结果.csv")

# 生成详细的权重分析报告
weight_analysis = pd.DataFrame({
    '重要性排名': range(1, len(importance_ranking) + 1),
    '指标名称': importance_ranking,
    'AHP权重': [ahp_weights[ind] for ind in importance_ranking],
    '权重占比(%)': [ahp_weights[ind] * 100 for ind in importance_ranking]
})

weight_analysis.to_csv('T3企业_AHP权重分析.csv', index=False, encoding='utf-8-sig')
print(f"✅ T3企业AHP权重分析已保存至: T3企业_AHP权重分析.csv")

print(f"\n" + "="*60)
print("🎉 T3企业AHP综合评估完成!")
print(f"✅ 基于九项指标计算综合得分")
print(f"✅ 抗风险指数权重最高: {ahp_weights['抗风险指数']:.4f}")
print(f"✅ 生成299家企业完整排名")
print("="*60)

print(f"\n下一步：使用遗传算法进行T3企业信贷资金分配优化")
