"""
第一题：基于PCA和AHP方法的T1企业信贷分配策略
为123家企业分别制定基于PCA和AHP综合得分的信贷分配方案
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

print("="*80)
print("第一题：T1企业信贷分配策略制定")
print("基于PCA方法和AHP方法的双重评估体系")
print("="*80)

# 读取PCA和AHP评估结果
print("读取评估结果数据...")
pca_results = pd.read_csv('PCA方法信贷投放策略.csv')
ahp_results = pd.read_csv('正确的AHP综合评估结果.csv')

print(f"PCA评估结果: {len(pca_results)}家企业")
print(f"AHP评估结果: {len(ahp_results)}家企业")

# 合并数据
merged_data = pd.merge(pca_results[['企业代号', 'PCA综合得分', 'PCA排名']], 
                       ahp_results[['企业代号', 'AHP综合得分', 'AHP排名', '信誉评级']], 
                       on='企业代号', how='inner')

print(f"合并后数据: {len(merged_data)}家企业")

class T1CreditAllocationStrategy:
    def __init__(self, data):
        self.data = data.copy()
        self.setup_rating_parameters()
        
    def setup_rating_parameters(self):
        """设置不同信誉评级的基础参数"""
        self.rating_params = {
            'A': {'base_rate': 4.0, 'max_amount': 1000, 'risk_factor': 0.8},
            'B': {'base_rate': 5.0, 'max_amount': 800, 'risk_factor': 1.0},
            'C': {'base_rate': 6.0, 'max_amount': 600, 'risk_factor': 1.2},
            'D': {'base_rate': 0.0, 'max_amount': 0, 'risk_factor': 2.0}  # 不放贷
        }
    
    def calculate_pca_strategy(self):
        """基于PCA综合得分制定信贷策略"""
        print("\n🎯 制定PCA信贷分配策略...")
        
        pca_strategy = []
        
        for _, enterprise in self.data.iterrows():
            pca_score = enterprise['PCA综合得分']
            pca_rank = enterprise['PCA排名']
            rating = enterprise['信誉评级']
            
            # 基于PCA得分的策略制定
            strategy = self._calculate_allocation_by_score(
                score=pca_score,
                rank=pca_rank, 
                rating=rating,
                method='PCA'
            )
            
            strategy['企业代号'] = enterprise['企业代号']
            strategy['PCA综合得分'] = pca_score
            strategy['PCA排名'] = pca_rank
            strategy['信誉评级'] = rating
            
            pca_strategy.append(strategy)
        
        pca_df = pd.DataFrame(pca_strategy)
        return pca_df
    
    def calculate_ahp_strategy(self):
        """基于AHP综合得分制定信贷策略"""
        print("🎯 制定AHP信贷分配策略...")
        
        ahp_strategy = []
        
        for _, enterprise in self.data.iterrows():
            ahp_score = enterprise['AHP综合得分']
            ahp_rank = enterprise['AHP排名']
            rating = enterprise['信誉评级']
            
            # 基于AHP得分的策略制定
            strategy = self._calculate_allocation_by_score(
                score=ahp_score,
                rank=ahp_rank,
                rating=rating, 
                method='AHP'
            )
            
            strategy['企业代号'] = enterprise['企业代号']
            strategy['AHP综合得分'] = ahp_score
            strategy['AHP排名'] = ahp_rank
            strategy['信誉评级'] = rating
            
            ahp_strategy.append(strategy)
        
        ahp_df = pd.DataFrame(ahp_strategy)
        return ahp_df
    
    def _calculate_allocation_by_score(self, score, rank, rating, method):
        """根据综合得分计算信贷分配策略"""
        
        # 排除D级企业
        if rating == 'D':
            return {
                f'{method}_建议放贷': '否',
                f'{method}_放贷金额(万元)': 0,
                f'{method}_建议利率(%)': 0,
                f'{method}_风险等级': '极高风险',
                f'{method}_策略说明': 'D级企业，不予放贷'
            }
        
        # 获取基础参数
        base_rate = self.rating_params[rating]['base_rate']
        max_amount = self.rating_params[rating]['max_amount']
        risk_factor = self.rating_params[rating]['risk_factor']
        
        # 基于综合得分调整参数
        if method == 'PCA':
            # PCA得分范围通常0-1
            score_factor = score
            # 根据排名进一步调整
            if rank <= 10:
                tier = 'S'  # 超优质
                amount_factor = 1.0
                rate_discount = -0.5
            elif rank <= 30:
                tier = 'A+'  # 优质
                amount_factor = 0.9
                rate_discount = -0.3
            elif rank <= 50:
                tier = 'A'   # 良好
                amount_factor = 0.8
                rate_discount = -0.1
            elif rank <= 70:
                tier = 'B'   # 一般
                amount_factor = 0.6
                rate_discount = 0.0
            else:
                tier = 'C'   # 较差
                amount_factor = 0.4
                rate_discount = 0.2
                
        else:  # AHP method
            # AHP得分范围通常0.4-0.7
            score_factor = (score - 0.4) / 0.3  # 标准化到0-1
            score_factor = max(0, min(1, score_factor))
            
            # 根据排名调整
            if rank <= 10:
                tier = 'S'
                amount_factor = 1.0
                rate_discount = -0.4
            elif rank <= 25:
                tier = 'A+'
                amount_factor = 0.9
                rate_discount = -0.2
            elif rank <= 50:
                tier = 'A'
                amount_factor = 0.75
                rate_discount = 0.0
            elif rank <= 75:
                tier = 'B'
                amount_factor = 0.6
                rate_discount = 0.2
            else:
                tier = 'C'
                amount_factor = 0.4
                rate_discount = 0.4
        
        # 计算最终参数
        loan_amount = max_amount * amount_factor * score_factor
        loan_amount = round(loan_amount, 0)
        
        interest_rate = base_rate + rate_discount + (1 - score_factor) * 0.5
        interest_rate = max(3.5, min(12.0, interest_rate))  # 限制在3.5%-12%
        interest_rate = round(interest_rate, 2)
        
        # 确定风险等级
        if score_factor >= 0.8 and rating in ['A', 'B']:
            risk_level = '低风险'
        elif score_factor >= 0.6:
            risk_level = '中低风险'
        elif score_factor >= 0.4:
            risk_level = '中等风险'
        elif score_factor >= 0.2:
            risk_level = '中高风险'
        else:
            risk_level = '高风险'
        
        # 是否放贷决策
        if loan_amount >= 50 and interest_rate <= 10:
            should_lend = '是'
            if loan_amount < 50:
                loan_amount = 50  # 最低放贷金额
        else:
            should_lend = '否'
            loan_amount = 0
            strategy_note = f'{method}评分过低或风险过高，不建议放贷'
        
        # 策略说明
        if should_lend == '是':
            strategy_note = f'{method}评分{score:.4f}，排名第{rank}，{tier}级客户，{risk_level}'
        else:
            strategy_note = f'{method}评分{score:.4f}，排名第{rank}，风险较高或评分不足'
        
        return {
            f'{method}_建议放贷': should_lend,
            f'{method}_放贷金额(万元)': int(loan_amount),
            f'{method}_建议利率(%)': interest_rate,
            f'{method}_风险等级': risk_level,
            f'{method}_策略说明': strategy_note
        }

    def generate_comparison_analysis(self, pca_df, ahp_df):
        """生成PCA与AHP策略对比分析"""
        print("\n📊 生成策略对比分析...")
        
        # 合并两种策略结果
        comparison = pd.merge(
            pca_df[['企业代号', '信誉评级', 'PCA综合得分', 'PCA排名', 'PCA_建议放贷', 'PCA_放贷金额(万元)', 'PCA_建议利率(%)', 'PCA_风险等级']],
            ahp_df[['企业代号', 'AHP综合得分', 'AHP排名', 'AHP_建议放贷', 'AHP_放贷金额(万元)', 'AHP_建议利率(%)', 'AHP_风险等级']],
            on='企业代号'
        )
        
        # 计算差异分析
        comparison['放贷决策一致'] = (comparison['PCA_建议放贷'] == comparison['AHP_建议放贷'])
        comparison['金额差异(万元)'] = comparison['AHP_放贷金额(万元)'] - comparison['PCA_放贷金额(万元)']
        comparison['利率差异(%)'] = comparison['AHP_建议利率(%)'] - comparison['PCA_建议利率(%)']
        
        return comparison
    
    def generate_summary_statistics(self, pca_df, ahp_df, comparison_df):
        """生成汇总统计"""
        print("\n📈 生成汇总统计...")
        
        stats = {}
        
        # PCA策略统计
        pca_lend = pca_df[pca_df['PCA_建议放贷'] == '是']
        stats['PCA'] = {
            '建议放贷企业数': len(pca_lend),
            '总放贷金额(万元)': pca_lend['PCA_放贷金额(万元)'].sum(),
            '平均放贷金额(万元)': pca_lend['PCA_放贷金额(万元)'].mean(),
            '平均利率(%)': pca_lend['PCA_建议利率(%)'].mean(),
            '信誉评级分布': pca_lend['信誉评级'].value_counts().to_dict(),
            '风险等级分布': pca_lend['PCA_风险等级'].value_counts().to_dict()
        }
        
        # AHP策略统计
        ahp_lend = ahp_df[ahp_df['AHP_建议放贷'] == '是']
        stats['AHP'] = {
            '建议放贷企业数': len(ahp_lend),
            '总放贷金额(万元)': ahp_lend['AHP_放贷金额(万元)'].sum(),
            '平均放贷金额(万元)': ahp_lend['AHP_放贷金额(万元)'].mean(),
            '平均利率(%)': ahp_lend['AHP_建议利率(%)'].mean(),
            '信誉评级分布': ahp_lend['信誉评级'].value_counts().to_dict(),
            '风险等级分布': ahp_lend['AHP_风险等级'].value_counts().to_dict()
        }
        
        # 一致性分析
        stats['对比分析'] = {
            '决策一致企业数': len(comparison_df[comparison_df['放贷决策一致'] == True]),
            '决策一致率(%)': len(comparison_df[comparison_df['放贷决策一致'] == True]) / len(comparison_df) * 100,
            '平均金额差异(万元)': comparison_df['金额差异(万元)'].mean(),
            '平均利率差异(%)': comparison_df['利率差异(%)'].mean()
        }
        
        return stats

# 主程序执行
if __name__ == "__main__":
    # 初始化策略制定器
    allocator = T1CreditAllocationStrategy(merged_data)
    
    # 制定PCA策略
    pca_strategy = allocator.calculate_pca_strategy()
    
    # 制定AHP策略
    ahp_strategy = allocator.calculate_ahp_strategy()
    
    # 生成对比分析
    comparison_analysis = allocator.generate_comparison_analysis(pca_strategy, ahp_strategy)
    
    # 生成统计分析
    summary_stats = allocator.generate_summary_statistics(pca_strategy, ahp_strategy, comparison_analysis)
    
    # 保存结果
    print("\n💾 保存策略结果...")
    pca_strategy.to_csv('T1_PCA信贷分配策略.csv', index=False, encoding='utf-8-sig')
    ahp_strategy.to_csv('T1_AHP信贷分配策略.csv', index=False, encoding='utf-8-sig')
    comparison_analysis.to_csv('T1_PCA与AHP策略对比分析.csv', index=False, encoding='utf-8-sig')
    
    # 输出摘要统计
    print("\n" + "="*80)
    print("📊 T1企业信贷分配策略摘要统计")
    print("="*80)
    
    print("\n1️⃣ PCA方法策略统计:")
    pca_stats = summary_stats['PCA']
    print(f"   建议放贷企业数: {pca_stats['建议放贷企业数']}家")
    print(f"   总放贷金额: {pca_stats['总放贷金额(万元)']:,.0f}万元")
    print(f"   平均放贷金额: {pca_stats['平均放贷金额(万元)']:,.1f}万元")
    print(f"   平均利率: {pca_stats['平均利率(%)']:.2f}%")
    print(f"   信誉评级分布: {pca_stats['信誉评级分布']}")
    print(f"   风险等级分布: {pca_stats['风险等级分布']}")
    
    print("\n2️⃣ AHP方法策略统计:")
    ahp_stats = summary_stats['AHP']
    print(f"   建议放贷企业数: {ahp_stats['建议放贷企业数']}家")
    print(f"   总放贷金额: {ahp_stats['总放贷金额(万元)']:,.0f}万元")
    print(f"   平均放贷金额: {ahp_stats['平均放贷金额(万元)']:,.1f}万元")
    print(f"   平均利率: {ahp_stats['平均利率(%)']:.2f}%")
    print(f"   信誉评级分布: {ahp_stats['信誉评级分布']}")
    print(f"   风险等级分布: {ahp_stats['风险等级分布']}")
    
    print("\n3️⃣ 策略对比分析:")
    comp_stats = summary_stats['对比分析']
    print(f"   决策一致企业数: {comp_stats['决策一致企业数']}家")
    print(f"   决策一致率: {comp_stats['决策一致率(%)']:.1f}%")
    print(f"   平均金额差异: {comp_stats['平均金额差异(万元)']:+.1f}万元")
    print(f"   平均利率差异: {comp_stats['平均利率差异(%)']:+.3f}%")
    
    # 显示部分策略结果
    print("\n4️⃣ PCA策略前10名企业:")
    pca_top10 = pca_strategy.head(10)[['企业代号', 'PCA综合得分', 'PCA排名', '信誉评级', 
                                      'PCA_建议放贷', 'PCA_放贷金额(万元)', 'PCA_建议利率(%)', 'PCA_风险等级']]
    print(pca_top10.to_string(index=False))
    
    print("\n5️⃣ AHP策略前10名企业:")
    ahp_top10 = ahp_strategy.head(10)[['企业代号', 'AHP综合得分', 'AHP排名', '信誉评级',
                                      'AHP_建议放贷', 'AHP_放贷金额(万元)', 'AHP_建议利率(%)', 'AHP_风险等级']]
    print(ahp_top10.to_string(index=False))
    
    print("\n" + "="*80)
    print("🎉 T1企业信贷分配策略制定完成！")
    print("📁 输出文件:")
    print("   - T1_PCA信贷分配策略.csv")
    print("   - T1_AHP信贷分配策略.csv") 
    print("   - T1_PCA与AHP策略对比分析.csv")
    print("="*80)
