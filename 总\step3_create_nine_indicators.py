"""
第三题步骤3：合并八项指标与抗风险指数，形成九项指标体系
将标准化后的抗风险指数加入T2企业的八项指标中，最终形成九项综合指标
"""
import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler

print("="*80)
print("第三题步骤3：形成九项指标体系")
print("="*80)

# 读取T2企业八项指标
t2_eight_indicators = pd.read_csv('T2企业_完整八项指标.csv')
print(f"T2企业八项指标: {len(t2_eight_indicators)}家企业")

# 读取抗风险指数
risk_resistance_data = pd.read_csv('T2企业_抗风险指数.csv')
print(f"抗风险指数数据: {len(risk_resistance_data)}家企业")

# 合并数据
t2_nine_indicators = pd.merge(
    t2_eight_indicators,
    risk_resistance_data[['企业代号', '抗风险指数']],
    on='企业代号',
    how='inner'
)

print(f"合并后数据: {len(t2_nine_indicators)}家企业")

# 定义九项指标
nine_indicators = [
    '供应商集中度',      # 1. 原有指标
    '客户集中度',        # 2. 原有指标  
    '流动比率',          # 3. 原有指标
    '资产负债率',        # 4. 原有指标
    '盈利预测可靠性',    # 5. 原有指标
    '税收贡献率',        # 6. 原有指标
    '历史违约次数',      # 7. 原有指标
    '负债水平',          # 8. 新增指标（基于信誉评级）
    '抗风险指数'         # 9. 新增指标（PCA计算得出）
]

print(f"\n✅ 完整九项指标体系:")
for i, indicator in enumerate(nine_indicators, 1):
    print(f"  {i:2d}. {indicator}")

# 检查所有指标是否存在
missing_indicators = []
for indicator in nine_indicators:
    if indicator not in t2_nine_indicators.columns:
        missing_indicators.append(indicator)

if missing_indicators:
    print(f"\n❌ 缺失指标: {missing_indicators}")
    exit(1)

# 统计九项指标的基本信息
print(f"\n九项指标统计摘要:")
print("指标名称           最小值      最大值      平均值      标准差")
print("-" * 65)

for indicator in nine_indicators:
    data = t2_nine_indicators[indicator]
    print(f"{indicator:15s} {data.min():9.3f} {data.max():9.3f} {data.mean():9.3f} {data.std():9.3f}")

# 对所有九项指标进行标准化处理
print(f"\n开始对九项指标进行标准化...")

# 定义指标方向性（正向指标越大越好，负向指标越小越好）
indicator_directions = {
    '供应商集中度': 'negative',      # 供应商越集中风险越大
    '客户集中度': 'negative',        # 客户越集中风险越大
    '流动比率': 'positive',          # 流动性越好越好
    '资产负债率': 'negative',        # 负债率越低越好
    '盈利预测可靠性': 'positive',    # 可靠性越高越好
    '税收贡献率': 'positive',        # 贡献率越高越好
    '历史违约次数': 'negative',      # 违约次数越少越好
    '负债水平': 'positive',          # 分数越高负债风险越低
    '抗风险指数': 'positive'         # 抗风险能力越强越好
}

print(f"\n指标方向性设定:")
for indicator, direction in indicator_directions.items():
    desc = "正向指标 (越大越好)" if direction == 'positive' else "负向指标 (越小越好)"
    print(f"  {indicator:15s}: {desc}")

# 创建标准化后的数据框
standardized_data = pd.DataFrame()
standardized_data['企业代号'] = t2_nine_indicators['企业代号']

scaler = MinMaxScaler()

for indicator in nine_indicators:
    original_data = t2_nine_indicators[indicator].values.reshape(-1, 1)
    
    if indicator_directions[indicator] == 'positive':
        # 正向指标：直接Min-Max标准化
        standardized_values = scaler.fit_transform(original_data).flatten()
    else:
        # 负向指标：1 - Min-Max标准化
        standardized_values = 1 - scaler.fit_transform(original_data).flatten()
    
    standardized_data[indicator] = standardized_values

# 添加信誉评级信息
standardized_data = pd.merge(
    standardized_data,
    t2_nine_indicators[['企业代号', '预测信誉评级']],
    on='企业代号',
    how='left'
)

print(f"\n✅ 标准化后的指标统计:")
print("指标名称           最小值    最大值    平均值    标准差")
print("-" * 60)

for indicator in nine_indicators:
    data = standardized_data[indicator]
    print(f"{indicator:15s} {data.min():7.4f} {data.max():7.4f} {data.mean():7.4f} {data.std():7.4f}")

# 验证标准化结果
print(f"\n✅ 标准化验证:")
all_in_range = True
for indicator in nine_indicators:
    data = standardized_data[indicator]
    if data.min() < -1e-10 or data.max() > 1 + 1e-10:  # 允许小的数值误差
        print(f"❌ {indicator} 不在[0,1]范围内: [{data.min():.6f}, {data.max():.6f}]")
        all_in_range = False

if all_in_range:
    print("✅ 所有指标都已正确标准化到[0,1]区间")

# 检查数据完整性
print(f"\n数据完整性检查:")
print(f"  企业总数: {len(standardized_data)}")
print(f"  缺失值总数: {standardized_data.isnull().sum().sum()}")
print(f"  重复企业: {standardized_data['企业代号'].duplicated().sum()}")

# 信誉评级分布
print(f"\n信誉评级分布:")
rating_dist = standardized_data['预测信誉评级'].value_counts().sort_index()
for rating, count in rating_dist.items():
    pct = count / len(standardized_data) * 100
    print(f"  {rating}级: {count:>3}家 ({pct:>5.1f}%)")

# 保存九项指标数据
# 保存原始数据
t2_nine_indicators_output = t2_nine_indicators[['企业代号'] + nine_indicators + ['预测信誉评级']]
t2_nine_indicators_output.to_csv('T2企业_完整九项指标_原始.csv', index=False, encoding='utf-8-sig')

# 保存标准化数据  
standardized_data.to_csv('T2企业_完整九项指标_标准化.csv', index=False, encoding='utf-8-sig')

print(f"\n✅ 九项指标数据保存完成:")
print(f"  原始数据: T2企业_完整九项指标_原始.csv")
print(f"  标准化数据: T2企业_完整九项指标_标准化.csv")

# 显示前10家企业的九项指标（标准化后）
print(f"\n前10家企业九项指标预览（标准化后）:")
pd.set_option('display.precision', 4)
pd.set_option('display.width', None)
pd.set_option('display.max_columns', None)

preview_data = standardized_data[['企业代号'] + nine_indicators[:5]].head(10)  # 只显示前5项指标避免过宽
print(preview_data)

print(f"\n✅ T2企业九项指标体系构建完成！")
print(f"   - 包含企业: {len(standardized_data)}家")
print(f"   - 指标数量: {len(nine_indicators)}项")
print(f"   - 已排除D级企业")
print(f"   - 所有指标已标准化到[0,1]区间")
print(f"\n下一步：等待进一步指示...")
