"""
步骤3：计算税务相关四项指标
为符合条件的99家企业计算税负压力、公司市场竞争力、盈利预测可靠性、经营风险
"""
import pandas as pd
import numpy as np

print("="*80)
print("步骤3：计算税务相关四项指标（仅针对符合条件的99家企业）")
print("="*80)

# 读取符合条件的企业名单
eligible_enterprises = pd.read_csv('符合条件企业名单.csv')
eligible_company_list = eligible_enterprises['企业代号'].tolist()

print(f"处理企业数量: {len(eligible_company_list)}家")

# 读取发票数据（只保留符合条件企业的数据）
data_input = pd.read_csv('2.csv')
data_output = pd.read_csv('3.csv')

data_input_filtered = data_input[data_input['企业代号'].isin(eligible_company_list)]
data_output_filtered = data_output[data_output['企业代号'].isin(eligible_company_list)]

print(f"筛选后进项发票: {len(data_input_filtered)}条")
print(f"筛选后销项发票: {len(data_output_filtered)}条")

def calculate_tax_indicators(company_list, input_data, output_data):
    """计算税务相关指标（仅使用有效发票）"""
    tax_indicators = {}
    
    print("  正在逐个企业计算税务指标...")
    
    for i, company in enumerate(company_list):
        if (i + 1) % 20 == 0:  # 每20家企业显示进度
            print(f"    已处理: {i+1}/{len(company_list)} 家企业")
            
        # 获取该企业的有效发票数据
        company_input = input_data[(input_data['企业代号'] == company) & 
                                 (input_data['发票状态'] == '有效发票')].copy()
        company_output = output_data[(output_data['企业代号'] == company) & 
                                   (output_data['发票状态'] == '有效发票')].copy()
        
        # 如果没有有效发票数据，设为默认值
        if len(company_input) == 0 or len(company_output) == 0:
            tax_indicators[company] = {
                '税负压力': 0,
                '公司市场竞争力': 0,
                '盈利预测可靠性': 0,
                '经营风险': 1.0  # 高风险
            }
            continue
        
        # 计算税率
        input_total_amount = company_input['金额'].sum()
        input_total_tax = company_input['税额'].sum()
        output_total_amount = company_output['金额'].sum()
        output_total_tax = company_output['税额'].sum()
        
        input_tax_rate = input_total_tax / input_total_amount if input_total_amount > 0 else 0
        output_tax_rate = output_total_tax / output_total_amount if output_total_amount > 0 else 0
        
        # 1. 税负压力 = 销项税率 - 进项税率（越大压力越大，负向指标）
        tax_pressure = output_tax_rate - input_tax_rate
        
        # 2. 公司市场竞争力 = 销项总额 / (进项总额 + 1)（销售能力）
        market_competitiveness = output_total_amount / (input_total_amount + 1)
        
        # 3. 盈利预测可靠性 = 销项发票数 / (进项发票数 + 销项发票数)
        total_invoices = len(company_input) + len(company_output)
        profit_predictability = len(company_output) / total_invoices if total_invoices > 0 else 0
        
        # 4. 经营风险 = |进项税率 - 销项税率| + 发票异常系数
        # 发票异常系数：发票数量不均衡的风险
        invoice_imbalance = abs(len(company_input) - len(company_output)) / (len(company_input) + len(company_output))
        business_risk = abs(input_tax_rate - output_tax_rate) + invoice_imbalance
        
        tax_indicators[company] = {
            '税负压力': tax_pressure,
            '公司市场竞争力': market_competitiveness,
            '盈利预测可靠性': profit_predictability,
            '经营风险': business_risk
        }
    
    print(f"    税务指标计算完成: {len(company_list)}家企业")
    return tax_indicators

# 计算税务指标
tax_indicators = calculate_tax_indicators(eligible_company_list, data_input_filtered, data_output_filtered)

# 转换为DataFrame
tax_df = pd.DataFrame.from_dict(tax_indicators, orient='index').reset_index()
tax_df.rename(columns={'index': '企业代号'}, inplace=True)

print(f"\n税务指标统计摘要:")
for col in ['税负压力', '公司市场竞争力', '盈利预测可靠性', '经营风险']:
    print(f"{col}: 最小值={tax_df[col].min():.4f}, 最大值={tax_df[col].max():.4f}, 平均值={tax_df[col].mean():.4f}")

# 保存税务指标
tax_df.to_csv('符合条件企业_税务四项指标.csv', index=False, encoding='utf-8-sig')
print(f"\n✅ 税务四项指标已保存至: 符合条件企业_税务四项指标.csv")

print(f"\n前10家企业税务指标预览:")
print("企业代号 | 税负压力 | 市场竞争力 | 盈利可靠性 | 经营风险")
print("-" * 60)
for _, row in tax_df.head(10).iterrows():
    print(f"{row['企业代号']:>6} | {row['税负压力']:>8.4f} | {row['公司市场竞争力']:>8.4f} | {row['盈利预测可靠性']:>8.4f} | {row['经营风险']:>8.4f}")

print(f"\n下一步：合并基础指标和税务指标，形成完整的八项指标")
