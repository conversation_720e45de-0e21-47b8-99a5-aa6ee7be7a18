"""
生成最终的T2企业数据集（去除D级企业）
整合标准化指标、相似度匹配结果和完整企业信息
"""
import pandas as pd
import numpy as np

print("="*80)
print("生成最终T2企业数据集（去除D级企业）")
print("="*80)

# 读取相关数据文件
print("📂 读取数据文件...")
similarity_results = pd.read_csv('T2企业_最终相似度匹配结果.csv')
normalized_indicators = pd.read_csv('T2企业_七项指标_标准化.csv') 
original_indicators = pd.read_csv('T2企业_七项指标.csv')

print(f"相似度匹配结果: {len(similarity_results)}家企业")
print(f"标准化指标数据: {len(normalized_indicators)}家企业") 
print(f"原始指标数据: {len(original_indicators)}家企业")

# 统计匹配结果
rating_stats = similarity_results['继承信誉评级'].value_counts()
print(f"\n📊 相似度匹配评级分布:")
for rating in ['A', 'B', 'C', 'D']:
    count = rating_stats.get(rating, 0)
    percentage = count / len(similarity_results) * 100
    print(f"  {rating}级: {count:>3d}家 ({percentage:5.1f}%)")

# 筛选出非D级企业
eligible_enterprises = similarity_results[similarity_results['继承信誉评级'] != 'D'].copy()
excluded_enterprises = similarity_results[similarity_results['继承信誉评级'] == 'D'].copy()

print(f"\n🎯 企业筛选结果:")
print(f"  可投放信贷企业: {len(eligible_enterprises)}家")
print(f"  排除D级企业: {len(excluded_enterprises)}家")

if len(excluded_enterprises) > 0:
    print(f"  排除的D级企业: {', '.join(excluded_enterprises['T2企业代号'].tolist())}")

# 合并数据创建最终数据集
print(f"\n🔧 整合企业完整数据...")

# 1. 基础数据：原始指标 + 相似度匹配结果
final_data = pd.merge(
    original_indicators, 
    eligible_enterprises[['T2企业代号', 'T1最相似企业', '继承信誉评级', '相似度得分']], 
    left_on='企业代号', right_on='T2企业代号', 
    how='inner'
)
final_data.drop('T2企业代号', axis=1, inplace=True)

# 2. 添加标准化指标（添加后缀以区分）
normalized_subset = normalized_indicators[normalized_indicators['企业代号'].isin(final_data['企业代号'])].copy()

# 重命名标准化指标列
standardized_cols = {}
for col in ['盈利能力', '现金流稳定性', '企业规模', '税负压力', '公司市场竞争力', '盈利预测可靠性', '经营风险']:
    standardized_cols[col] = col + '_标准化'

normalized_subset = normalized_subset.rename(columns=standardized_cols)

# 合并标准化数据
final_data = pd.merge(
    final_data, 
    normalized_subset, 
    on='企业代号', 
    how='left'
)

print(f"最终数据集: {len(final_data)}家企业, {len(final_data.columns)}个字段")

# 3. 数据质量检查
print(f"\n✅ 数据质量检查:")
print(f"  数据完整性: {final_data.isnull().sum().sum() == 0}")
print(f"  评级分布一致性: {len(final_data) == len(eligible_enterprises)}")
print(f"  企业代号唯一性: {final_data['企业代号'].nunique() == len(final_data)}")

# 4. 最终评级统计
final_rating_stats = final_data['继承信誉评级'].value_counts()
print(f"\n📊 最终数据集评级分布:")
total = len(final_data)
for rating in ['A', 'B', 'C']:
    count = final_rating_stats.get(rating, 0)
    percentage = count / total * 100
    print(f"  {rating}级: {count:>3d}家 ({percentage:5.1f}%)")

# 5. 显示数据结构
print(f"\n📋 数据集字段结构:")
print("原始指标字段:")
original_cols = ['企业代号', '盈利能力', '现金流稳定性', '企业规模', '税负压力', '公司市场竞争力', '盈利预测可靠性', '经营风险']
for col in original_cols:
    print(f"  - {col}")

print("相似度匹配字段:")
matching_cols = ['T1最相似企业', '继承信誉评级', '相似度得分']
for col in matching_cols:
    print(f"  - {col}")

print("标准化指标字段:")
for col in standardized_cols.values():
    print(f"  - {col}")

# 6. 样本数据展示
print(f"\n📋 前10家企业样本数据:")
print("企业代号 | 评级 | 原始盈利能力 | 标准化盈利 | 原始企业规模 | 标准化规模 | 相似度得分")
print("-" * 95)
for _, row in final_data.head(10).iterrows():
    print(f"{row['企业代号']:>6s} | {row['继承信誉评级']:>2s} | {row['盈利能力']:>10.0f} | "
          f"{row['盈利能力_标准化']:>8.4f} | {row['企业规模']:>10.0f} | "
          f"{row['企业规模_标准化']:>8.4f} | {row['相似度得分']:>8.4f}")

# 7. 各评级企业的指标特征分析
print(f"\n📈 各评级企业指标特征分析:")
for rating in ['A', 'B', 'C']:
    rating_data = final_data[final_data['继承信誉评级'] == rating]
    if len(rating_data) > 0:
        avg_profit = rating_data['盈利能力'].mean()
        avg_scale = rating_data['企业规模'].mean() 
        avg_similarity = rating_data['相似度得分'].mean()
        print(f"  {rating}级企业({len(rating_data)}家): 平均盈利{avg_profit:>8.0f}, "
              f"平均规模{avg_scale:>10.0f}, 平均相似度{avg_similarity:.4f}")

# 8. 保存最终数据集
output_files = [
    ('T2企业_最终投放数据集.csv', '完整的T2投放企业数据集（原始+标准化+匹配信息）'),
    ('T2企业_优化算法输入.csv', '用于优化算法的核心数据（企业代号+评级+标准化指标）')
]

# 完整数据集
final_data.to_csv(output_files[0][0], index=False, encoding='utf-8-sig')

# 优化算法输入数据（精简版）
algorithm_input = final_data[[
    '企业代号', '继承信誉评级', '相似度得分',
    '盈利能力_标准化', '现金流稳定性_标准化', '企业规模_标准化', 
    '税负压力_标准化', '公司市场竞争力_标准化', 
    '盈利预测可靠性_标准化', '经营风险_标准化'
]].copy()

algorithm_input.to_csv(output_files[1][0], index=False, encoding='utf-8-sig')

print(f"\n✅ 数据文件保存完成:")
for filename, description in output_files:
    print(f"  - {filename}: {description}")

# 9. 为多目标优化准备的数据摘要
print(f"\n🎯 多目标优化数据摘要:")
print(f"  投放企业总数: {len(final_data)}家")
print(f"  信贷资金总额: 1亿元")
print(f"  利率区间: 4%-15%")
print(f"  评级结构: A级{final_rating_stats['A']}家 + B级{final_rating_stats['B']}家 + C级{final_rating_stats['C']}家")
print(f"  优化目标: 期望收益最大化 + 风险最小化（权重相同）")
print(f"  约束条件: 总资金≤1亿元, 4%≤利率≤15%, 风险分散")

print(f"\n🎉 T2企业最终数据集生成完成！")
print(f"可以开始设计启发式优化算法进行1亿元信贷资金的最优配置。")
