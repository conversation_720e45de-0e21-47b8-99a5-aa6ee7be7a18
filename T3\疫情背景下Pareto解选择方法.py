"""
疫情背景下Pareto最优解的客观选择方法
基于疫情阶段、银行风险偏好、监管要求等多维度客观评估
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

print("="*80)
print("疫情背景下Pareto最优解的客观选择方法")
print("="*80)

class PandemicParetoSelector:
    def __init__(self):
        # 读取Pareto最优解数据
        self.pareto_data = pd.read_csv('T3_Pareto最优解分析.csv')
        print(f"读取到 {len(self.pareto_data)} 个Pareto最优解")
        
    def method1_pandemic_stage_selection(self):
        """方法1：基于疫情阶段的客观选择"""
        print("\n📊 方法1：基于疫情阶段的动态选择")
        
        # 定义疫情阶段特征
        pandemic_stages = {
            '疫情爆发期': {
                'risk_tolerance': 0.2,      # 风险容忍度极低
                'liquidity_priority': 0.9,  # 流动性优先级极高
                'growth_priority': 0.1,     # 增长优先级极低
                'description': '银行需要保存实力，避免大额损失'
            },
            '疫情控制期': {
                'risk_tolerance': 0.4,      # 风险容忍度较低
                'liquidity_priority': 0.7,  # 流动性优先级高
                'growth_priority': 0.3,     # 增长优先级较低
                'description': '谨慎恢复业务，平衡风险与收益'
            },
            '疫情缓解期': {
                'risk_tolerance': 0.6,      # 风险容忍度中等
                'liquidity_priority': 0.5,  # 流动性优先级中等
                'growth_priority': 0.5,     # 增长优先级中等
                'description': '平衡发展，适度承担风险'
            },
            '疫情恢复期': {
                'risk_tolerance': 0.8,      # 风险容忍度较高
                'liquidity_priority': 0.3,  # 流动性优先级较低
                'growth_priority': 0.7,     # 增长优先级高
                'description': '积极扩张，追求增长机会'
            }
        }
        
        # 为每个阶段选择最适合的方案
        stage_recommendations = {}
        
        for stage, params in pandemic_stages.items():
            # 计算综合适应度分数
            scores = []
            for _, row in self.pareto_data.iterrows():
                # 标准化指标
                norm_revenue = row['期望年收益(万元)'] / self.pareto_data['期望年收益(万元)'].max()
                norm_loss = 1 - (row['流失损失(万元)'] / self.pareto_data['流失损失(万元)'].max())
                
                # 计算适应度分数
                score = (params['growth_priority'] * norm_revenue + 
                        params['liquidity_priority'] * norm_loss)
                scores.append(score)
            
            # 选择得分最高的方案
            best_idx = np.argmax(scores)
            best_solution = self.pareto_data.iloc[best_idx]
            
            stage_recommendations[stage] = {
                'solution': best_solution,
                'score': scores[best_idx],
                'params': params
            }
            
            print(f"\n🎯 {stage}推荐方案：{best_solution['方案名称']}")
            print(f"   期望收益：{best_solution['期望年收益(万元)']:.2f}万元")
            print(f"   流失损失：{best_solution['流失损失(万元)']:.2f}万元")
            print(f"   适应度分数：{scores[best_idx]:.3f}")
            print(f"   理由：{params['description']}")
        
        return stage_recommendations
    
    def method2_regulatory_constraint_selection(self):
        """方法2：基于监管约束的客观选择"""
        print("\n📊 方法2：基于监管约束的客观选择")
        
        # 监管约束参数（基于银保监会相关规定）
        regulatory_constraints = {
            '风险损失率上限': 2.0,      # 损失率不超过2%
            '最低收益率要求': 3.0,      # 收益率不低于3%
            '预算利用率下限': 20.0,     # 预算利用率不低于20%
            '放贷企业数下限': 30,       # 至少服务30家企业
        }
        
        print("监管约束条件：")
        for constraint, value in regulatory_constraints.items():
            print(f"  - {constraint}: {value}")
        
        # 筛选满足监管要求的方案
        compliant_solutions = []
        
        for _, row in self.pareto_data.iterrows():
            if (row['损失率(%)'] <= regulatory_constraints['风险损失率上限'] and
                row['收益率(%)'] >= regulatory_constraints['最低收益率要求'] and
                row['预算利用率(%)'] >= regulatory_constraints['预算利用率下限'] and
                row['放贷企业数'] >= regulatory_constraints['放贷企业数下限']):
                
                compliant_solutions.append(row)
        
        if compliant_solutions:
            compliant_df = pd.DataFrame(compliant_solutions)
            
            # 在合规方案中选择净收益最高的
            best_compliant = compliant_df.loc[compliant_df['净收益(万元)'].idxmax()]
            
            print(f"\n✅ 监管合规方案数量：{len(compliant_solutions)}个")
            print(f"🏆 推荐方案：{best_compliant['方案名称']}")
            print(f"   净收益：{best_compliant['净收益(万元)']:.2f}万元")
            print(f"   损失率：{best_compliant['损失率(%)']:.2f}%")
            print(f"   收益率：{best_compliant['收益率(%)']:.2f}%")
            
            return best_compliant
        else:
            print("❌ 没有方案满足所有监管约束")
            return None
    
    def method3_multi_criteria_decision(self):
        """方法3：多准则决策分析（TOPSIS方法）"""
        print("\n📊 方法3：多准则决策分析（TOPSIS方法）")
        
        # 选择关键指标
        criteria = ['期望年收益(万元)', '净收益(万元)', '收益率(%)', '放贷企业数']
        weights = [0.3, 0.4, 0.2, 0.1]  # 权重：净收益最重要
        
        # 提取决策矩阵
        decision_matrix = self.pareto_data[criteria].values
        
        # 标准化决策矩阵
        normalized_matrix = decision_matrix / np.sqrt(np.sum(decision_matrix**2, axis=0))
        
        # 加权标准化矩阵
        weighted_matrix = normalized_matrix * weights
        
        # 确定正理想解和负理想解
        positive_ideal = np.max(weighted_matrix, axis=0)
        negative_ideal = np.min(weighted_matrix, axis=0)
        
        # 计算到正理想解和负理想解的距离
        pos_distances = np.sqrt(np.sum((weighted_matrix - positive_ideal)**2, axis=1))
        neg_distances = np.sqrt(np.sum((weighted_matrix - negative_ideal)**2, axis=1))
        
        # 计算相对接近度
        relative_closeness = neg_distances / (pos_distances + neg_distances)
        
        # 排序并选择最优方案
        best_idx = np.argmax(relative_closeness)
        best_solution = self.pareto_data.iloc[best_idx]
        
        print(f"评价指标权重：")
        for criterion, weight in zip(criteria, weights):
            print(f"  - {criterion}: {weight}")
        
        print(f"\n🏆 TOPSIS最优方案：{best_solution['方案名称']}")
        print(f"   相对接近度：{relative_closeness[best_idx]:.3f}")
        print(f"   期望收益：{best_solution['期望年收益(万元)']:.2f}万元")
        print(f"   净收益：{best_solution['净收益(万元)']:.2f}万元")
        
        # 返回所有方案的TOPSIS得分
        topsis_results = self.pareto_data.copy()
        topsis_results['TOPSIS得分'] = relative_closeness
        topsis_results = topsis_results.sort_values('TOPSIS得分', ascending=False)
        
        return topsis_results
    
    def method4_scenario_analysis(self):
        """方法4：情景分析法"""
        print("\n📊 方法4：情景分析法")
        
        # 定义三种情景
        scenarios = {
            '乐观情景': {
                'probability': 0.3,
                'revenue_multiplier': 1.2,    # 收益增加20%
                'loss_multiplier': 0.8,       # 损失减少20%
                'description': '疫情快速结束，经济强劲复苏'
            },
            '基准情景': {
                'probability': 0.5,
                'revenue_multiplier': 1.0,    # 收益不变
                'loss_multiplier': 1.0,       # 损失不变
                'description': '疫情逐步控制，经济缓慢恢复'
            },
            '悲观情景': {
                'probability': 0.2,
                'revenue_multiplier': 0.8,    # 收益减少20%
                'loss_multiplier': 1.3,       # 损失增加30%
                'description': '疫情反复，经济持续低迷'
            }
        }
        
        # 计算每个方案在不同情景下的期望净收益
        scenario_results = []
        
        for _, row in self.pareto_data.iterrows():
            expected_values = []
            
            for scenario, params in scenarios.items():
                adjusted_revenue = row['期望年收益(万元)'] * params['revenue_multiplier']
                adjusted_loss = row['流失损失(万元)'] * params['loss_multiplier']
                adjusted_net = adjusted_revenue - adjusted_loss
                
                expected_value = adjusted_net * params['probability']
                expected_values.append(expected_value)
            
            total_expected = sum(expected_values)
            
            scenario_results.append({
                '方案名称': row['方案名称'],
                '期望净收益': total_expected,
                '乐观情景净收益': (row['期望年收益(万元)'] * scenarios['乐观情景']['revenue_multiplier'] - 
                              row['流失损失(万元)'] * scenarios['乐观情景']['loss_multiplier']),
                '基准情景净收益': row['净收益(万元)'],
                '悲观情景净收益': (row['期望年收益(万元)'] * scenarios['悲观情景']['revenue_multiplier'] - 
                              row['流失损失(万元)'] * scenarios['悲观情景']['loss_multiplier'])
            })
        
        scenario_df = pd.DataFrame(scenario_results)
        scenario_df = scenario_df.sort_values('期望净收益', ascending=False)
        
        best_scenario = scenario_df.iloc[0]
        
        print("情景设定：")
        for scenario, params in scenarios.items():
            print(f"  {scenario}（概率{params['probability']}）：{params['description']}")
        
        print(f"\n🏆 情景分析最优方案：{best_scenario['方案名称']}")
        print(f"   期望净收益：{best_scenario['期望净收益']:.2f}万元")
        print(f"   乐观情景：{best_scenario['乐观情景净收益']:.2f}万元")
        print(f"   基准情景：{best_scenario['基准情景净收益']:.2f}万元")
        print(f"   悲观情景：{best_scenario['悲观情景净收益']:.2f}万元")
        
        return scenario_df
    
    def method5_risk_adjusted_selection(self):
        """方法5：风险调整收益率选择"""
        print("\n📊 方法5：风险调整收益率选择")
        
        # 计算风险调整收益率（类似夏普比率）
        risk_adjusted_results = []
        
        for _, row in self.pareto_data.iterrows():
            # 使用损失率作为风险指标
            risk = row['损失率(%)'] / 100
            return_rate = row['收益率(%)'] / 100
            
            # 假设无风险利率为3%
            risk_free_rate = 0.03
            
            # 计算风险调整收益率
            if risk > 0:
                risk_adjusted_return = (return_rate - risk_free_rate) / risk
            else:
                risk_adjusted_return = float('inf')
            
            risk_adjusted_results.append({
                '方案名称': row['方案名称'],
                '收益率': return_rate,
                '风险率': risk,
                '风险调整收益率': risk_adjusted_return,
                '净收益': row['净收益(万元)']
            })
        
        risk_df = pd.DataFrame(risk_adjusted_results)
        risk_df = risk_df.sort_values('风险调整收益率', ascending=False)
        
        best_risk_adjusted = risk_df.iloc[0]
        
        print(f"🏆 风险调整收益率最优方案：{best_risk_adjusted['方案名称']}")
        print(f"   风险调整收益率：{best_risk_adjusted['风险调整收益率']:.2f}")
        print(f"   收益率：{best_risk_adjusted['收益率']*100:.2f}%")
        print(f"   风险率：{best_risk_adjusted['风险率']*100:.2f}%")
        print(f"   净收益：{best_risk_adjusted['净收益']:.2f}万元")
        
        return risk_df
    
    def comprehensive_recommendation(self):
        """综合推荐"""
        print("\n" + "="*80)
        print("🎯 综合推荐结果")
        print("="*80)
        
        # 运行所有方法
        stage_rec = self.method1_pandemic_stage_selection()
        regulatory_rec = self.method2_regulatory_constraint_selection()
        topsis_rec = self.method3_multi_criteria_decision()
        scenario_rec = self.method4_scenario_analysis()
        risk_rec = self.method5_risk_adjusted_selection()
        
        # 统计各方案被推荐的次数
        recommendation_count = {}
        
        # 疫情阶段推荐
        for stage, rec in stage_rec.items():
            solution_name = rec['solution']['方案名称']
            recommendation_count[solution_name] = recommendation_count.get(solution_name, 0) + 1
        
        # 其他方法推荐
        if regulatory_rec is not None:
            recommendation_count[regulatory_rec['方案名称']] = recommendation_count.get(regulatory_rec['方案名称'], 0) + 1
        
        recommendation_count[topsis_rec.iloc[0]['方案名称']] = recommendation_count.get(topsis_rec.iloc[0]['方案名称'], 0) + 1
        recommendation_count[scenario_rec.iloc[0]['方案名称']] = recommendation_count.get(scenario_rec.iloc[0]['方案名称'], 0) + 1
        recommendation_count[risk_rec.iloc[0]['方案名称']] = recommendation_count.get(risk_rec.iloc[0]['方案名称'], 0) + 1
        
        # 找出被推荐次数最多的方案
        most_recommended = max(recommendation_count, key=recommendation_count.get)
        
        print(f"\n📊 各方案推荐次数统计：")
        for solution, count in sorted(recommendation_count.items(), key=lambda x: x[1], reverse=True):
            print(f"   {solution}: {count}次")
        
        print(f"\n🏆 综合推荐方案：{most_recommended}")
        print(f"   推荐次数：{recommendation_count[most_recommended]}次")
        
        # 显示该方案的详细信息
        recommended_solution = self.pareto_data[self.pareto_data['方案名称'] == most_recommended].iloc[0]
        print(f"\n📋 方案详情：")
        print(f"   期望收益：{recommended_solution['期望年收益(万元)']:.2f}万元")
        print(f"   流失损失：{recommended_solution['流失损失(万元)']:.2f}万元")
        print(f"   净收益：{recommended_solution['净收益(万元)']:.2f}万元")
        print(f"   收益率：{recommended_solution['收益率(%)']:.2f}%")
        print(f"   损失率：{recommended_solution['损失率(%)']:.2f}%")
        print(f"   放贷企业数：{recommended_solution['放贷企业数']}家")
        
        return most_recommended, recommendation_count

if __name__ == "__main__":
    selector = PandemicParetoSelector()
    final_recommendation, counts = selector.comprehensive_recommendation()
    
    print(f"\n✅ 分析完成！在疫情背景下，推荐选择：{final_recommendation}")
