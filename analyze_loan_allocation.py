#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
T2企业贷款分配分析：为什么有些企业没有获得贷款？
"""
import pandas as pd
import numpy as np

def analyze_loan_allocation():
    print('='*80)
    print('T2企业贷款分配深度分析：为什么有些企业没有获得贷款？')
    print('='*80)
    
    # 读取数据
    all_enterprises = pd.read_csv('T2/T2企业_最终完整数据.csv')
    allocated_enterprises = pd.read_csv('T2/T2_推荐信贷分配方案.csv')
    
    print(f'\n📊 基本统计:')
    print(f'   - 总企业数: {len(all_enterprises)}家')
    print(f'   - 获得贷款企业数: {len(allocated_enterprises)}家')
    print(f'   - 未获得贷款企业数: {len(all_enterprises) - len(allocated_enterprises)}家')
    print(f'   - 贷款覆盖率: {len(allocated_enterprises)/len(all_enterprises):.1%}')
    
    # 分析未获得贷款的企业
    allocated_codes = set(allocated_enterprises['企业代号'])
    all_codes = set(all_enterprises['企业代号'])
    not_allocated_codes = all_codes - allocated_codes
    
    not_allocated_enterprises = all_enterprises[all_enterprises['企业代号'].isin(not_allocated_codes)]
    
    print(f'\n🚫 未获得贷款企业分析:')
    print(f'   按信誉评级分布:')
    rating_dist = not_allocated_enterprises['继承信誉评级'].value_counts()
    total_not_allocated = len(not_allocated_enterprises)
    for rating, count in rating_dist.items():
        percentage = count / total_not_allocated * 100
        print(f'     - {rating}级: {count}家 ({percentage:.1f}%)')
    
    print(f'\n✅ 获得贷款企业分析:')
    print(f'   按信誉评级分布:')
    allocated_rating_dist = allocated_enterprises['信誉评级'].value_counts()
    total_allocated = len(allocated_enterprises)
    for rating, count in allocated_rating_dist.items():
        percentage = count / total_allocated * 100
        print(f'     - {rating}级: {count}家 ({percentage:.1f}%)')
    
    # 分析各评级的贷款获得率
    print(f'\n📈 各信誉评级贷款获得率:')
    for rating in ['A', 'B', 'C']:
        total_rating = len(all_enterprises[all_enterprises['继承信誉评级'] == rating])
        allocated_rating = len(allocated_enterprises[allocated_enterprises['信誉评级'] == rating])
        if total_rating > 0:
            rate = allocated_rating / total_rating * 100
            print(f'   - {rating}级: {allocated_rating}/{total_rating} ({rate:.1f}%)')
    
    # 分析相似度得分的影响
    print(f'\n🎯 相似度得分对贷款分配的影响:')
    
    # 获得贷款企业的相似度得分
    allocated_similarity = allocated_enterprises['相似度得分']
    print(f'   获得贷款企业的相似度得分:')
    print(f'     - 最小值: {allocated_similarity.min():.4f}')
    print(f'     - 最大值: {allocated_similarity.max():.4f}')
    print(f'     - 平均值: {allocated_similarity.mean():.4f}')
    print(f'     - 中位数: {allocated_similarity.median():.4f}')
    
    # 未获得贷款企业的相似度得分
    not_allocated_similarity = not_allocated_enterprises['相似度得分']
    print(f'   未获得贷款企业的相似度得分:')
    print(f'     - 最小值: {not_allocated_similarity.min():.4f}')
    print(f'     - 最大值: {not_allocated_similarity.max():.4f}')
    print(f'     - 平均值: {not_allocated_similarity.mean():.4f}')
    print(f'     - 中位数: {not_allocated_similarity.median():.4f}')
    
    # 分析相似度阈值
    similarity_threshold = allocated_similarity.min()
    print(f'\n   📏 相似度阈值分析:')
    print(f'     - 获得贷款的最低相似度: {similarity_threshold:.4f}')
    
    below_threshold = not_allocated_enterprises[not_allocated_enterprises['相似度得分'] < similarity_threshold]
    print(f'     - 低于此阈值的未贷款企业: {len(below_threshold)}家')
    
    # 分析预算约束的影响
    print(f'\n💰 预算约束分析:')
    total_budget = 100_000_000  # 1亿元
    used_budget = allocated_enterprises['贷款金额(元)'].sum()
    remaining_budget = total_budget - used_budget
    
    print(f'   - 总预算: {total_budget:,}元')
    print(f'   - 已使用预算: {used_budget:,}元')
    print(f'   - 剩余预算: {remaining_budget:,}元')
    print(f'   - 预算利用率: {used_budget/total_budget:.1%}')
    
    # 分析单企业额度约束的影响
    min_loan = 100_000  # 10万元
    max_loan = 1_000_000  # 100万元
    
    print(f'\n🏦 单企业额度约束分析:')
    print(f'   - 最小贷款额度: {min_loan:,}元')
    print(f'   - 最大贷款额度: {max_loan:,}元')
    
    # 计算如果给所有剩余企业最小额度需要多少资金
    remaining_enterprises = len(not_allocated_enterprises)
    min_budget_needed = remaining_enterprises * min_loan
    
    print(f'   - 剩余企业数: {remaining_enterprises}家')
    print(f'   - 给所有剩余企业最小额度需要: {min_budget_needed:,}元')
    print(f'   - 当前剩余预算: {remaining_budget:,}元')
    
    if min_budget_needed > remaining_budget:
        max_additional_enterprises = remaining_budget // min_loan
        print(f'   - 预算不足！最多还能放贷: {max_additional_enterprises}家企业')
    else:
        print(f'   - 预算充足，可以给所有剩余企业放贷')
    
    # 分析优化算法的选择策略
    print(f'\n🧠 优化算法选择策略分析:')
    print(f'   算法优先考虑的因素:')
    print(f'   1. 收益最大化：选择高相似度、低流失率的企业')
    print(f'   2. 风险最小化：避免高风险企业')
    print(f'   3. 预算约束：在有限预算下选择最优组合')
    print(f'   4. 额度约束：每家企业10万-100万元限制')
    
    # 分析未获得贷款的具体原因
    print(f'\n🔍 未获得贷款的主要原因分析:')
    
    # 原因1：相似度得分过低
    low_similarity = not_allocated_enterprises[not_allocated_enterprises['相似度得分'] < allocated_similarity.mean()]
    print(f'   1. 相似度得分过低: {len(low_similarity)}家 ({len(low_similarity)/len(not_allocated_enterprises)*100:.1f}%)')
    
    # 原因2：信誉评级较差
    low_rating = not_allocated_enterprises[not_allocated_enterprises['继承信誉评级'] == 'C']
    print(f'   2. 信誉评级为C级: {len(low_rating)}家 ({len(low_rating)/len(not_allocated_enterprises)*100:.1f}%)')
    
    # 原因3：预算限制
    print(f'   3. 预算限制: 剩余预算{remaining_budget:,}元，无法满足所有企业最小额度需求')
    
    # 原因4：算法优化选择
    print(f'   4. 算法优化选择: NSGA-II算法基于多目标优化，选择了收益风险比最优的企业组合')
    
    # 给出改进建议
    print(f'\n💡 改进建议:')
    print(f'   1. 增加预算：如需覆盖更多企业，可考虑增加总预算')
    print(f'   2. 调整额度：降低最小贷款额度（如5万元）可覆盖更多企业')
    print(f'   3. 分层策略：对不同评级企业采用不同的贷款策略')
    print(f'   4. 动态调整：根据市场情况动态调整选择标准')
    
    # 展示一些未获得贷款的企业样本
    print(f'\n📋 未获得贷款企业样本（前10家）:')
    sample_not_allocated = not_allocated_enterprises.head(10)[['企业代号', '继承信誉评级', '相似度得分']]
    for _, row in sample_not_allocated.iterrows():
        print(f'   - {row["企业代号"]}: {row["继承信誉评级"]}级, 相似度{row["相似度得分"]:.4f}')
    
    print('\n' + '='*80)
    print('✅ 分析完成！')
    print('总结：企业未获得贷款主要由于相似度得分较低、信誉评级较差、')
    print('预算约束以及算法的多目标优化选择策略综合作用的结果。')
    print('='*80)

if __name__ == "__main__":
    analyze_loan_allocation()
