"""
T1企业信贷资金分配策略
基于PCA和AHP综合得分的资金分配比例计算
"""
import pandas as pd
import numpy as np

print("="*80)
print("T1企业信贷资金分配策略")
print("基于综合得分的资金分配比例计算")
print("="*80)

# 读取PCA和AHP策略结果
pca_strategy = pd.read_csv('PCA方法信贷投放策略.csv')
ahp_strategy = pd.read_csv('AHP信贷投放策略.csv')

print(f"PCA策略企业数: {len(pca_strategy)}家")
print(f"AHP策略企业数: {len(ahp_strategy)}家")

def calculate_allocation_percentage(data, score_column):
    """基于综合得分计算资金分配比例"""
    
    # 计算权重（得分平方，放大高分企业优势）
    weights = data[score_column] ** 2
    
    # 标准化为百分比
    total_weight = weights.sum()
    percentages = (weights / total_weight * 100).round(4)
    
    return percentages

# 计算PCA资金分配策略
print(f"\n🔹 PCA方法资金分配策略")
print("="*50)

pca_percentages = calculate_allocation_percentage(pca_strategy, 'PCA综合得分')
pca_strategy['资金分配比例(%)'] = pca_percentages

# 显示PCA策略结果（前20名）
print("前20名企业资金分配：")
print("排名 | 企业代号 | PCA得分 | 信誉评级 | 资金分配比例(%)")
print("-" * 55)
for idx, row in pca_strategy.head(20).iterrows():
    print(f"{row['PCA排名']:>3d} | {row['企业代号']:>6s} | {row['PCA综合得分']:>7.4f} | {row['信誉评级']:>6s} | {row['资金分配比例(%)']:>10.4f}")

print(f"\n前20名企业合计分配比例: {pca_strategy.head(20)['资金分配比例(%)'].sum():.2f}%")
print(f"前50名企业合计分配比例: {pca_strategy.head(50)['资金分配比例(%)'].sum():.2f}%")

# 按信誉评级统计PCA分配
print(f"\nPCA策略按信誉评级分配统计：")
pca_rating_stats = pca_strategy.groupby('信誉评级')['资金分配比例(%)'].agg(['count', 'sum']).round(2)
pca_rating_stats.columns = ['企业数量', '资金分配比例(%)']
print(pca_rating_stats)

# 计算AHP资金分配策略
print(f"\n🔹 AHP方法资金分配策略")
print("="*50)

ahp_percentages = calculate_allocation_percentage(ahp_strategy, 'AHP综合得分')
ahp_strategy['资金分配比例(%)'] = ahp_percentages

# 显示AHP策略结果（前20名）
print("前20名企业资金分配：")
print("排名 | 企业代号 | AHP得分 | 信誉评级 | 资金分配比例(%)")
print("-" * 55)
for idx, row in ahp_strategy.head(20).iterrows():
    print(f"{row['AHP排名']:>3d} | {row['企业代号']:>6s} | {row['AHP综合得分']:>7.4f} | {row['信誉评级']:>6s} | {row['资金分配比例(%)']:>10.4f}")

print(f"\n前20名企业合计分配比例: {ahp_strategy.head(20)['资金分配比例(%)'].sum():.2f}%")
print(f"前50名企业合计分配比例: {ahp_strategy.head(50)['资金分配比例(%)'].sum():.2f}%")

# 按信誉评级统计AHP分配
print(f"\nAHP策略按信誉评级分配统计：")
ahp_rating_stats = ahp_strategy.groupby('信誉评级')['资金分配比例(%)'].agg(['count', 'sum']).round(2)
ahp_rating_stats.columns = ['企业数量', '资金分配比例(%)']
print(ahp_rating_stats)

# 对比分析
print(f"\n🔸 PCA vs AHP 策略对比")
print("="*50)

# 找到两种方法的前10名企业
pca_top10 = set(pca_strategy.head(10)['企业代号'])
ahp_top10 = set(ahp_strategy.head(10)['企业代号'])

common_top10 = pca_top10.intersection(ahp_top10)
print(f"两种方法共同前10名企业: {len(common_top10)}家")
print(f"共同企业: {sorted(common_top10)}")

print(f"\n仅PCA方法前10名: {sorted(pca_top10 - common_top10)}")
print(f"仅AHP方法前10名: {sorted(ahp_top10 - common_top10)}")

# 集中度分析
print(f"\n资金集中度分析：")
print(f"PCA方法 - 前10%企业({len(pca_strategy)//10}家)获得资金比例: {pca_strategy.head(len(pca_strategy)//10)['资金分配比例(%)'].sum():.2f}%")
print(f"AHP方法 - 前10%企业({len(ahp_strategy)//10}家)获得资金比例: {ahp_strategy.head(len(ahp_strategy)//10)['资金分配比例(%)'].sum():.2f}%")

# 保存完整分配结果
pca_result = pca_strategy[['企业代号', 'PCA综合得分', 'PCA排名', '信誉评级', '资金分配比例(%)', '风险等级', '建议利率(%)']]
ahp_result = ahp_strategy[['企业代号', 'AHP综合得分', 'AHP排名', '信誉评级', '资金分配比例(%)', '风险等级', '建议利率(%)']]

pca_result.to_csv('T1_PCA资金分配策略.csv', index=False, encoding='utf-8-sig')
ahp_result.to_csv('T1_AHP资金分配策略.csv', index=False, encoding='utf-8-sig')

print(f"\n✅ 资金分配结果已保存:")
print(f"📊 T1_PCA资金分配策略.csv - PCA方法分配结果")
print(f"📊 T1_AHP资金分配策略.csv - AHP方法分配结果")

print(f"\n" + "="*80)
print("🎉 T1企业信贷资金分配策略制定完成！")
print("="*80)
