#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
遗传算法详细过程演示
通过具体例子展示遗传算法的每一步操作
"""

import pandas as pd
import numpy as np
import random
from scipy.interpolate import interp1d

class GeneticAlgorithmDemo:
    def __init__(self):
        # 简化版：只考虑前10家企业进行演示
        self.enterprises = pd.read_csv('T2企业_最终完整数据.csv').head(10)
        self.n_enterprises = len(self.enterprises)
        
        # 基础参数
        self.TOTAL_BUDGET = 10_000_000  # 简化为1000万
        self.MIN_LOAN = 100_000
        self.MAX_LOAN = 1_000_000
        self.MIN_RATE = 0.04
        self.MAX_RATE = 0.15
        
        # 简化的流失率函数
        self.churn_rates = {
            'A': lambda r: 0.1 + (r - 0.04) * 2,
            'B': lambda r: 0.2 + (r - 0.04) * 3,
            'C': lambda r: 0.3 + (r - 0.04) * 4
        }
        
        print(f"🧬 遗传算法过程演示")
        print(f"   - 简化企业数量: {self.n_enterprises}家")
        print(f"   - 简化预算: {self.TOTAL_BUDGET:,}元")
        print(f"   - 个体长度: {2 * self.n_enterprises}个基因")
    
    def create_individual(self, method='random'):
        """创建个体（染色体）"""
        individual = []
        
        if method == 'random':
            # 完全随机初始化
            for _ in range(self.n_enterprises):
                individual.append(random.uniform(0, 1))  # 贷款比例
            for _ in range(self.n_enterprises):
                individual.append(random.uniform(self.MIN_RATE, self.MAX_RATE))  # 利率
        
        elif method == 'heuristic':
            # 启发式初始化
            rating_weights = {'A': 0.5, 'B': 0.3, 'C': 0.2}
            selected_count = random.randint(5, 8)  # 随机选择5-8家企业
            
            # 选择企业
            selected = random.sample(range(self.n_enterprises), selected_count)
            
            for i in range(self.n_enterprises):
                if i in selected:
                    rating = self.enterprises.iloc[i]['继承信誉评级']
                    weight = rating_weights.get(rating, 0.2)
                    ratio = weight * random.uniform(0.5, 1.5) / selected_count
                    individual.append(min(1.0, ratio))
                else:
                    individual.append(0.0)
            
            # 利率基于评级
            for i in range(self.n_enterprises):
                rating = self.enterprises.iloc[i]['继承信誉评级']
                if rating == 'A':
                    rate = random.uniform(0.04, 0.08)
                elif rating == 'B':
                    rate = random.uniform(0.06, 0.10)
                else:
                    rate = random.uniform(0.08, 0.12)
                individual.append(rate)
        
        return individual
    
    def decode_individual(self, individual):
        """解码个体：将基因转换为实际的贷款方案"""
        print(f"\n🔍 解码个体:")
        
        # 分离基因
        loan_ratios = np.array(individual[:self.n_enterprises])
        interest_rates = np.array(individual[self.n_enterprises:])
        
        print(f"   原始贷款比例: {loan_ratios}")
        print(f"   原始利率: {interest_rates}")
        
        # 归一化贷款金额
        total_ratio = np.sum(loan_ratios)
        if total_ratio > 0:
            normalized_ratios = loan_ratios / total_ratio
            loan_amounts = normalized_ratios * self.TOTAL_BUDGET
        else:
            loan_amounts = np.zeros(self.n_enterprises)
        
        print(f"   归一化后贷款金额: {loan_amounts}")
        
        # 应用约束
        for i in range(len(loan_amounts)):
            if loan_amounts[i] > 0:
                if loan_amounts[i] < self.MIN_LOAN:
                    loan_amounts[i] = 0
                elif loan_amounts[i] > self.MAX_LOAN:
                    loan_amounts[i] = self.MAX_LOAN
        
        print(f"   约束后贷款金额: {loan_amounts}")
        
        # 重新归一化
        total_loan = np.sum(loan_amounts)
        if total_loan > self.TOTAL_BUDGET:
            loan_amounts = loan_amounts * (self.TOTAL_BUDGET / total_loan)
        
        print(f"   最终贷款金额: {loan_amounts}")
        print(f"   最终利率: {interest_rates}")
        
        return loan_amounts, interest_rates
    
    def evaluate_individual(self, individual):
        """评估个体适应度"""
        loan_amounts, interest_rates = self.decode_individual(individual)
        
        total_objective = 0.0
        details = []
        
        print(f"\n📊 适应度评估:")
        
        for i in range(self.n_enterprises):
            loan_amount = loan_amounts[i]
            interest_rate = interest_rates[i]
            
            if loan_amount >= self.MIN_LOAN:
                rating = self.enterprises.iloc[i]['继承信誉评级']
                churn_rate = self.churn_rates[rating](interest_rate)
                churn_rate = max(0, min(1, churn_rate))
                
                expected_revenue = loan_amount * interest_rate * (1 - churn_rate)
                interest_loss = loan_amount * interest_rate * churn_rate
                objective_contribution = expected_revenue - interest_loss
                
                total_objective += objective_contribution
                
                details.append({
                    'enterprise': self.enterprises.iloc[i]['企业代号'],
                    'rating': rating,
                    'loan': loan_amount,
                    'rate': interest_rate,
                    'churn': churn_rate,
                    'revenue': expected_revenue,
                    'loss': interest_loss,
                    'contribution': objective_contribution
                })
                
                print(f"   {self.enterprises.iloc[i]['企业代号']} ({rating}级): "
                      f"贷款{loan_amount:,.0f}, 利率{interest_rate:.2%}, "
                      f"流失率{churn_rate:.2%}, 贡献{objective_contribution:,.0f}")
        
        print(f"   总目标值: {total_objective:,.0f}元")
        
        return total_objective, details
    
    def crossover(self, parent1, parent2):
        """交叉操作：双点交叉"""
        print(f"\n🧬 交叉操作:")
        print(f"   父代1: {parent1[:5]}...{parent1[-5:]}")
        print(f"   父代2: {parent2[:5]}...{parent2[-5:]}")
        
        size = len(parent1)
        point1 = random.randint(1, size - 2)
        point2 = random.randint(point1 + 1, size - 1)
        
        print(f"   交叉点: {point1}, {point2}")
        
        # 创建子代
        child1 = parent1[:point1] + parent2[point1:point2] + parent1[point2:]
        child2 = parent2[:point1] + parent1[point1:point2] + parent2[point2:]
        
        print(f"   子代1: {child1[:5]}...{child1[-5:]}")
        print(f"   子代2: {child2[:5]}...{child2[-5:]}")
        
        return child1, child2
    
    def mutate(self, individual, mutation_prob=0.1):
        """变异操作"""
        print(f"\n🔀 变异操作 (概率={mutation_prob}):")
        print(f"   变异前: {individual[:5]}...{individual[-5:]}")
        
        mutated = individual.copy()
        mutations = []
        
        for i in range(len(mutated)):
            if random.random() < mutation_prob:
                old_value = mutated[i]
                
                if i < self.n_enterprises:  # 贷款比例基因
                    mutated[i] += random.gauss(0, 0.1)
                    mutated[i] = max(0.0, min(1.0, mutated[i]))
                else:  # 利率基因
                    mutated[i] += random.gauss(0, 0.01)
                    mutated[i] = max(self.MIN_RATE, min(self.MAX_RATE, mutated[i]))
                
                mutations.append(f"位置{i}: {old_value:.3f} → {mutated[i]:.3f}")
        
        if mutations:
            print(f"   发生变异: {', '.join(mutations)}")
        else:
            print(f"   未发生变异")
        
        print(f"   变异后: {mutated[:5]}...{mutated[-5:]}")
        
        return mutated
    
    def tournament_selection(self, population, fitnesses, tournament_size=3):
        """锦标赛选择"""
        print(f"\n🏆 锦标赛选择 (锦标赛规模={tournament_size}):")
        
        # 随机选择参赛个体
        tournament_indices = random.sample(range(len(population)), tournament_size)
        tournament_fitnesses = [fitnesses[i] for i in tournament_indices]
        
        print(f"   参赛个体适应度: {[f'{f:,.0f}' for f in tournament_fitnesses]}")
        
        # 选择最优个体
        winner_index = tournament_indices[np.argmax(tournament_fitnesses)]
        winner_fitness = fitnesses[winner_index]
        
        print(f"   获胜者: 个体{winner_index}, 适应度{winner_fitness:,.0f}")
        
        return population[winner_index]
    
    def demonstrate_full_process(self):
        """演示完整的遗传算法过程"""
        print(f"\n" + "="*80)
        print(f"🧬 遗传算法完整过程演示")
        print(f"="*80)
        
        # 第1步：初始化种群
        print(f"\n📍 第1步：初始化种群")
        population_size = 4  # 简化为4个个体
        population = []
        
        for i in range(population_size):
            method = 'heuristic' if i < 2 else 'random'
            individual = self.create_individual(method)
            population.append(individual)
            print(f"   个体{i+1} ({method}): 前5个基因 {individual[:5]}")
        
        # 第2步：评估种群
        print(f"\n📍 第2步：评估初始种群")
        fitnesses = []
        for i, individual in enumerate(population):
            print(f"\n--- 评估个体{i+1} ---")
            fitness, _ = self.evaluate_individual(individual)
            fitnesses.append(fitness)
        
        print(f"\n初始种群适应度: {[f'{f:,.0f}' for f in fitnesses]}")
        
        # 第3步：选择
        print(f"\n📍 第3步：选择操作")
        selected_parents = []
        for i in range(2):  # 选择2个父代
            parent = self.tournament_selection(population, fitnesses)
            selected_parents.append(parent)
        
        # 第4步：交叉
        print(f"\n📍 第4步：交叉操作")
        child1, child2 = self.crossover(selected_parents[0], selected_parents[1])
        
        # 第5步：变异
        print(f"\n📍 第5步：变异操作")
        child1_mutated = self.mutate(child1)
        child2_mutated = self.mutate(child2)
        
        # 第6步：评估新个体
        print(f"\n📍 第6步：评估新个体")
        print(f"\n--- 评估子代1 ---")
        fitness1, _ = self.evaluate_individual(child1_mutated)
        print(f"\n--- 评估子代2 ---")
        fitness2, _ = self.evaluate_individual(child2_mutated)
        
        print(f"\n新个体适应度: 子代1={fitness1:,.0f}, 子代2={fitness2:,.0f}")
        
        # 第7步：环境选择（更新种群）
        print(f"\n📍 第7步：环境选择")
        all_individuals = population + [child1_mutated, child2_mutated]
        all_fitnesses = fitnesses + [fitness1, fitness2]
        
        # 选择最优的4个个体
        sorted_indices = np.argsort(all_fitnesses)[::-1]  # 降序排列
        new_population = [all_individuals[i] for i in sorted_indices[:population_size]]
        new_fitnesses = [all_fitnesses[i] for i in sorted_indices[:population_size]]
        
        print(f"   更新后种群适应度: {[f'{f:,.0f}' for f in new_fitnesses]}")
        print(f"   最佳适应度提升: {max(fitnesses):,.0f} → {max(new_fitnesses):,.0f}")
        
        return new_population, new_fitnesses

if __name__ == "__main__":
    demo = GeneticAlgorithmDemo()
    
    print(f"\n🎯 企业信息:")
    for i, (_, enterprise) in enumerate(demo.enterprises.iterrows()):
        print(f"   {enterprise['企业代号']}: {enterprise['继承信誉评级']}级, "
              f"相似度{enterprise['相似度得分']:.3f}")
    
    # 演示完整过程
    final_population, final_fitnesses = demo.demonstrate_full_process()
    
    print(f"\n" + "="*80)
    print(f"🎉 演示完成！")
    print(f"   这就是遗传算法的一次完整迭代过程")
    print(f"   在实际应用中，这个过程会重复数百次直到收敛")
    print(f"="*80)
