"""
步骤6：基于标准化指标计算AHP综合得分
使用之前确定的AHP权重计算最终得分并排序
"""
import pandas as pd
import numpy as np

print("="*80)
print("步骤6：计算AHP综合得分并排序")
print("="*80)

# 读取标准化指标
standardized_data = pd.read_csv('符合条件企业_标准化八项指标.csv')
print(f"参与评估的企业数量: {len(standardized_data)}家")

# AHP权重（根据之前的分析确定的权重）
ahp_weights = {
    '月利润中位数': 0.2637,
    '企业规模': 0.1318, 
    '信用评分': 0.2637,
    '稳定性': 0.0659,
    '税负压力': 0.0659,
    '公司市场竞争力': 0.1318,
    '盈利预测可靠性': 0.0659,
    '经营风险': 0.0114
}

print("✅ AHP权重分配:")
for indicator, weight in ahp_weights.items():
    print(f"  {indicator:12s}: {weight:6.4f}")

# 验证权重和为1
total_weight = sum(ahp_weights.values())
print(f"\n权重总和验证: {total_weight:.4f} (应为1.0000)")

# 计算AHP综合得分
ahp_scores = []
indicator_columns = list(ahp_weights.keys())

for idx, row in standardized_data.iterrows():
    score = 0
    for indicator in indicator_columns:
        score += row[indicator] * ahp_weights[indicator]
    ahp_scores.append(score)

# 添加AHP得分到数据框
result_data = standardized_data[['企业代号']].copy()
result_data['AHP综合得分'] = ahp_scores

# 按得分降序排序
result_data = result_data.sort_values('AHP综合得分', ascending=False).reset_index(drop=True)
result_data['AHP排名'] = range(1, len(result_data) + 1)

print(f"\n✅ AHP综合得分统计:")
print(f"最高分: {result_data['AHP综合得分'].max():.4f}")
print(f"最低分: {result_data['AHP综合得分'].min():.4f}")
print(f"平均分: {result_data['AHP综合得分'].mean():.4f}")

# 读取原始企业信息以便添加信誉评级
original_data = pd.read_csv('符合条件企业名单.csv')
result_data = pd.merge(result_data, original_data[['企业代号', '信誉评级']], on='企业代号', how='left')

print(f"\n前20名企业AHP综合评估结果:")
print("排名 | 企业代号 | AHP得分 | 信誉评级")
print("-" * 35)
for idx, row in result_data.head(20).iterrows():
    print(f"{row['AHP排名']:>3d} | {row['企业代号']:>6s} | {row['AHP综合得分']:7.4f} | {row['信誉评级']:>6s}")

print(f"\n后10名企业AHP综合评估结果:")
print("排名 | 企业代号 | AHP得分 | 信誉评级")
print("-" * 35)
for idx, row in result_data.tail(10).iterrows():
    print(f"{row['AHP排名']:>3d} | {row['企业代号']:>6s} | {row['AHP综合得分']:7.4f} | {row['信誉评级']:>6s}")

# 按信誉评级分组分析
print(f"\n按信誉评级分组的AHP得分分析:")
rating_analysis = result_data.groupby('信誉评级')['AHP综合得分'].agg(['count', 'mean', 'min', 'max']).round(4)
rating_analysis.columns = ['企业数量', '平均得分', '最低得分', '最高得分']
print(rating_analysis)

# 保存AHP评估结果
result_data.to_csv('符合条件企业_AHP综合评估结果.csv', index=False, encoding='utf-8-sig')
print(f"\n✅ AHP综合评估结果已保存至: 符合条件企业_AHP综合评估结果.csv")

# 验证排名的正确性
print(f"\n✅ 排名验证:")
is_sorted_desc = (result_data['AHP综合得分'].diff().dropna() <= 0).all()
print(f"- 按得分降序排列: {is_sorted_desc}")
print(f"- 排名连续性: {(result_data['AHP排名'] == range(1, len(result_data)+1)).all()}")

print(f"\n下一步：基于AHP排名确定风险等级和利率")
