"""
第三题 步骤6：T3企业多目标遗传算法信贷分配优化
基于T3 AHP排名，考虑疫情冲击下的风险调整，使用NSGA-II算法进行信贷资金分配
目标：在1亿元预算约束下，最大化收益并最小化风险
"""
import pandas as pd
import numpy as np
import random
from sklearn.preprocessing import MinMaxScaler
import warnings
warnings.filterwarnings('ignore')

print("="*80)
print("第三题 步骤6：T3企业多目标遗传算法信贷分配优化")
print("="*80)

class T3CreditAllocationOptimizer:
    def __init__(self, t3_data, budget=100_000_000):
        self.t3_data = t3_data.copy()
        self.budget = budget
        self.population_size = 100
        self.generations = 300
        self.crossover_prob = 0.8
        self.mutation_prob = 0.1
        
        # 预处理数据
        self.preprocess_data()
        
    def preprocess_data(self):
        """预处理T3企业数据"""
        print("预处理T3企业数据...")
        
        # 基础参数设置
        self.t3_data['最大贷款额'] = np.clip(
            self.t3_data['T3_AHP综合得分'] * 20_000_000,  # 基于AHP得分
            500_000,    # 最小50万
            10_000_000  # 最大1000万
        )
        
        # 基于T3排名和信誉评级制定利率策略
        def calculate_t3_interest_rate(row):
            ranking = row['T3_AHP排名']
            rating = row.get('继承信誉评级', 'B')
            
            # T3特殊调整：考虑疫情冲击，整体利率上调0.5%
            base_adjustment = 0.5
            
            if ranking <= 100:  # 前100名：低风险组
                if rating == 'A':
                    rate = 4.5 + (ranking - 1) * 0.03 + base_adjustment
                elif rating == 'B':
                    rate = 5.0 + (ranking - 1) * 0.03 + base_adjustment  
                else:  # C
                    rate = 5.5 + (ranking - 1) * 0.03 + base_adjustment
            elif ranking <= 200:  # 101-200名：中风险组
                if rating == 'A':
                    rate = 7.5 + (ranking - 101) * 0.05 + base_adjustment
                elif rating == 'B':
                    rate = 8.5 + (ranking - 101) * 0.05 + base_adjustment
                else:  # C
                    rate = 9.5 + (ranking - 101) * 0.05 + base_adjustment
            else:  # 201-299名：高风险组
                if rating == 'A':
                    rate = 12.5 + (ranking - 201) * 0.08 + base_adjustment
                elif rating == 'B':
                    rate = 13.5 + (ranking - 201) * 0.08 + base_adjustment
                else:  # C
                    rate = 14.5 + (ranking - 201) * 0.08 + base_adjustment
                    
            return min(rate, 15.0)  # 最高不超过15%
        
        self.t3_data['利率(%)'] = self.t3_data.apply(calculate_t3_interest_rate, axis=1)
        
        # T3风险损失率计算（考虑疫情额外风险）
        def calculate_t3_risk_loss_rate(row):
            base_rate = (15.1 - row['利率(%)']) / 100 * 0.1  # 基础风险率
            
            # 疫情额外风险
            pandemic_risk = 0.02  # 疫情基础风险2%
            
            # 抗风险指数调整
            risk_resistance = row.get('抗风险指数', 0.1)
            pandemic_adjustment = pandemic_risk * (1 - risk_resistance)
            
            total_risk = base_rate + pandemic_adjustment
            return min(total_risk, 0.15)  # 最高15%风险损失率
        
        self.t3_data['风险损失率(%)'] = self.t3_data.apply(calculate_t3_risk_loss_rate, axis=1)
        
        # 客户流失率（基于利率）
        self.t3_data['客户流失率(%)'] = np.clip(
            (self.t3_data['利率(%)'] - 4) * 0.8 + 1,
            0, 8
        )
        
        print(f"✅ T3企业数据预处理完成: {len(self.t3_data)}家企业")
        print(f"   利率范围: {self.t3_data['利率(%)'].min():.2f}%-{self.t3_data['利率(%)'].max():.2f}%")
        print(f"   风险损失率范围: {self.t3_data['风险损失率(%)'].min():.3f}-{self.t3_data['风险损失率(%)'].max():.3f}")
        
    def fitness_function(self, individual):
        """适应度函数：计算预期收益和风险损失"""
        total_loan = 0
        total_return = 0
        total_risk = 0
        
        for i, loan_amount in enumerate(individual):
            if loan_amount > 0:
                company = self.t3_data.iloc[i]
                
                # 预期年收益
                annual_return = loan_amount * (company['利率(%)'] / 100)
                
                # 预期风险损失
                risk_loss = loan_amount * company['风险损失率(%)']
                
                total_loan += loan_amount
                total_return += annual_return  
                total_risk += risk_loss
        
        # 预算约束惩罚
        budget_penalty = max(0, total_loan - self.budget) * 0.1
        
        return total_return - budget_penalty, total_risk + budget_penalty
    
    def generate_individual(self):
        """生成个体（信贷分配方案）"""
        individual = [0.0] * len(self.t3_data)
        remaining_budget = self.budget
        
        # 启发式初始化：优先给高排名企业分配资金
        sorted_indices = list(range(len(self.t3_data)))
        random.shuffle(sorted_indices)
        
        # 前50%概率优先选择排名靠前的企业
        if random.random() < 0.5:
            sorted_indices.sort(key=lambda x: self.t3_data.iloc[x]['T3_AHP排名'])
        
        allocated_count = 0
        max_allocations = min(150, len(self.t3_data))  # 最多分配150家企业
        
        for i in sorted_indices:
            if allocated_count >= max_allocations or remaining_budget <= 0:
                break
                
            max_loan = min(
                self.t3_data.iloc[i]['最大贷款额'],
                remaining_budget,
                remaining_budget / (max_allocations - allocated_count) * 2
            )
            
            if max_loan >= 500_000:  # 最小贷款额50万
                loan_amount = random.uniform(500_000, max_loan)
                individual[i] = loan_amount
                remaining_budget -= loan_amount
                allocated_count += 1
        
        return individual
    
    def crossover(self, parent1, parent2):
        """交叉操作"""
        if random.random() > self.crossover_prob:
            return parent1.copy(), parent2.copy()
        
        child1 = parent1.copy()
        child2 = parent2.copy()
        
        # 两点交叉
        point1, point2 = sorted(random.sample(range(len(parent1)), 2))
        
        child1[point1:point2] = parent2[point1:point2]
        child2[point1:point2] = parent1[point1:point2]
        
        return child1, child2
    
    def mutate(self, individual):
        """变异操作"""
        if random.random() > self.mutation_prob:
            return individual
        
        mutated = individual.copy()
        
        # 选择变异位置
        idx = random.randint(0, len(individual) - 1)
        
        if random.random() < 0.5:
            # 调整现有贷款额
            if mutated[idx] > 0:
                max_loan = self.t3_data.iloc[idx]['最大贷款额']
                mutated[idx] = random.uniform(0, max_loan)
        else:
            # 新增或删除贷款
            if mutated[idx] == 0:
                max_loan = self.t3_data.iloc[idx]['最大贷款额']
                mutated[idx] = random.uniform(500_000, max_loan)
            else:
                mutated[idx] = 0
        
        return mutated
    
    def non_dominated_sort(self, population):
        """非支配排序"""
        fitness_values = [self.fitness_function(ind) for ind in population]
        
        fronts = []
        domination_count = [0] * len(population)
        dominated_solutions = [[] for _ in range(len(population))]
        
        # 计算支配关系
        for i in range(len(population)):
            for j in range(len(population)):
                if i != j:
                    # 比较两个目标：收益最大化，风险最小化
                    if (fitness_values[i][0] >= fitness_values[j][0] and 
                        fitness_values[i][1] <= fitness_values[j][1] and
                        (fitness_values[i][0] > fitness_values[j][0] or 
                         fitness_values[i][1] < fitness_values[j][1])):
                        dominated_solutions[i].append(j)
                    elif (fitness_values[j][0] >= fitness_values[i][0] and 
                          fitness_values[j][1] <= fitness_values[i][1] and
                          (fitness_values[j][0] > fitness_values[i][0] or 
                           fitness_values[j][1] < fitness_values[i][1])):
                        domination_count[i] += 1
        
        # 第一前沿
        front = []
        for i in range(len(population)):
            if domination_count[i] == 0:
                front.append(i)
        fronts.append(front)
        
        # 后续前沿
        while len(fronts[-1]) > 0:
            next_front = []
            for i in fronts[-1]:
                for j in dominated_solutions[i]:
                    domination_count[j] -= 1
                    if domination_count[j] == 0:
                        next_front.append(j)
            fronts.append(next_front)
        
        return fronts[:-1], fitness_values
    
    def optimize(self):
        """执行NSGA-II优化"""
        print(f"\n开始T3企业信贷分配优化...")
        print(f"参数: 种群{self.population_size}, 代数{self.generations}, 预算{self.budget:,}元")
        
        # 初始化种群
        population = [self.generate_individual() for _ in range(self.population_size)]
        
        best_solutions = []
        
        for generation in range(self.generations):
            # 非支配排序
            fronts, fitness_values = self.non_dominated_sort(population)
            
            # 记录当代最优解
            if len(fronts[0]) > 0:
                best_idx = fronts[0][0]
                best_solutions.append((population[best_idx].copy(), fitness_values[best_idx]))
            
            # 选择父代
            new_population = []
            
            # 精英保留
            for front in fronts:
                if len(new_population) + len(front) <= self.population_size:
                    new_population.extend([population[i] for i in front])
                else:
                    remaining = self.population_size - len(new_population)
                    selected = random.sample(front, remaining)
                    new_population.extend([population[i] for i in selected])
                    break
            
            # 生成子代
            offspring = []
            for i in range(0, len(new_population) - 1, 2):
                parent1, parent2 = new_population[i], new_population[i + 1]
                child1, child2 = self.crossover(parent1, parent2)
                offspring.extend([self.mutate(child1), self.mutate(child2)])
            
            population = new_population + offspring[:self.population_size - len(new_population)]
            
            if (generation + 1) % 50 == 0:
                print(f"  第{generation + 1}代完成")
        
        # 最终非支配排序，获取Pareto前沿
        fronts, fitness_values = self.non_dominated_sort(population)
        pareto_solutions = [(population[i], fitness_values[i]) for i in fronts[0]]
        
        return pareto_solutions, best_solutions

# 执行优化
print("读取T3企业AHP评估结果（含利率风险）...")
t3_results = pd.read_csv('T3企业_AHP综合评估结果_含利率风险.csv')

print(f"T3企业数据: {len(t3_results)}家企业")
print(f"数据列: {list(t3_results.columns)}")

# 初始化优化器
optimizer = T3CreditAllocationOptimizer(t3_results)

# 执行优化
pareto_solutions, evolution = optimizer.optimize()

print(f"\n✅ T3企业信贷分配优化完成!")
print(f"找到 {len(pareto_solutions)} 个Pareto最优解")

# 分析Pareto解
pareto_analysis = []
for i, (solution, fitness) in enumerate(pareto_solutions):
    total_loan = sum(solution)
    allocated_companies = sum(1 for x in solution if x > 0)
    
    # 按信誉评级分组统计
    a_loan = sum(solution[j] for j in range(len(solution)) 
                if solution[j] > 0 and t3_results.iloc[j].get('继承信誉评级') == 'A')
    b_loan = sum(solution[j] for j in range(len(solution)) 
                if solution[j] > 0 and t3_results.iloc[j].get('继承信誉评级') == 'B')
    c_loan = sum(solution[j] for j in range(len(solution)) 
                if solution[j] > 0 and t3_results.iloc[j].get('继承信誉评级') == 'C')
    
    a_count = sum(1 for j in range(len(solution)) 
                 if solution[j] > 0 and t3_results.iloc[j].get('继承信誉评级') == 'A')
    b_count = sum(1 for j in range(len(solution)) 
                 if solution[j] > 0 and t3_results.iloc[j].get('继承信誉评级') == 'B')
    c_count = sum(1 for j in range(len(solution)) 
                 if solution[j] > 0 and t3_results.iloc[j].get('继承信誉评级') == 'C')
    
    # 计算平均利率
    weighted_rate = sum(solution[j] * t3_results.iloc[j]['利率(%)'] 
                       for j in range(len(solution)) if solution[j] > 0) / max(total_loan, 1)
    
    pareto_analysis.append({
        '解编号': i + 1,
        '总放贷额(元)': total_loan,
        '总收益(元)': fitness[0],
        '总风险(元)': fitness[1],
        '放贷企业数': allocated_companies,
        'A级分配(元)': a_loan,
        'B级分配(元)': b_loan,
        'C级分配(元)': c_loan,
        'A级企业数': a_count,
        'B级企业数': b_count,
        'C级企业数': c_count,
        '平均利率(%)': weighted_rate,
        '收益风险比': fitness[0] / max(fitness[1], 1)
    })

# 保存Pareto分析结果
pareto_df = pd.DataFrame(pareto_analysis)
pareto_df = pareto_df.sort_values('收益风险比', ascending=False)
pareto_df.to_csv('T3_Pareto最优解汇总.csv', index=False, encoding='utf-8-sig')

print(f"✅ Pareto最优解分析已保存至: T3_Pareto最优解汇总.csv")

# 选择推荐方案（收益风险比最高的方案）
best_solution_idx = pareto_df['收益风险比'].idxmax()
best_solution = pareto_solutions[best_solution_idx][0]

# 生成详细分配方案
allocation_details = []
for i, loan_amount in enumerate(best_solution):
    if loan_amount > 0:
        company = t3_results.iloc[i]
        allocation_details.append({
            '企业代号': company['企业代号'],
            'T3_AHP排名': company['T3_AHP排名'],
            'T3_AHP得分': company['T3_AHP综合得分'],
            '信誉评级': company.get('继承信誉评级', 'N/A'),
            '贷款金额(元)': loan_amount,
            '贷款利率(%)': company['利率(%)'],
            '预期年收益(元)': loan_amount * company['利率(%)'] / 100,
            '预期风险损失(元)': loan_amount * company['风险损失率'],
            '风险损失率': company['风险损失率']
        })

allocation_df = pd.DataFrame(allocation_details)
allocation_df = allocation_df.sort_values('贷款金额(元)', ascending=False)
allocation_df.to_csv('T3_推荐信贷分配方案.csv', index=False, encoding='utf-8-sig')

print(f"✅ 推荐信贷分配方案已保存至: T3_推荐信贷分配方案.csv")

# 显示推荐方案摘要
recommended = pareto_df.iloc[0]
print(f"\n📊 T3推荐信贷分配方案摘要:")
print(f"   总放贷额: {recommended['总放贷额(元)']:,.0f}元")
print(f"   预算利用率: {recommended['总放贷额(元)']/100_000_000:.1%}")
print(f"   放贷企业数: {recommended['放贷企业数']}家")
print(f"   预期总收益: {recommended['总收益(元)']:,.0f}元")
print(f"   预期总风险: {recommended['总风险(元)']:,.0f}元")
print(f"   收益风险比: {recommended['收益风险比']:.2f}")
print(f"   平均利率: {recommended['平均利率(%)']:.2f}%")

print(f"\n🎉 T3企业信贷分配优化完成！")
print("="*80)
