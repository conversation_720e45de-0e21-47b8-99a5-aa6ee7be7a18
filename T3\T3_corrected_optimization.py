"""
T3企业双目标信贷分配优化（修正版）
目标1：最大化期望收益
目标2：最小化流失损失
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.interpolate import interp1d
from scipy.optimize import differential_evolution
import warnings
warnings.filterwarnings('ignore')

print("="*80)
print("T3企业双目标信贷分配优化（修正版）")
print("目标1: 最大化期望收益")  
print("目标2: 最小化流失损失")
print("="*80)

class T3CorrectedOptimizer:
    def __init__(self):
        # 约束条件
        self.total_budget = 1e8  # 1亿元
        self.min_loan = 1e5      # 10万元
        self.max_loan = 1e6      # 100万元
        self.min_rate = 0.04     # 4%
        self.max_rate = 0.15     # 15%
        
        # 读取数据
        self.load_data()
        self.setup_interpolation()
        
    def load_data(self):
        """加载T3企业AHP评估结果和利率-流失率关系"""
        print("读取T3企业AHP评估结果...")
        self.t3_data = pd.read_csv('T3企业_AHP综合评估结果.csv')
        print(f"T3企业数据: {len(self.t3_data)}家企业")
        
        print("读取利率-客户流失率关系...")
        rate_data = pd.read_csv('4.csv')
        
        # 解析利率数据
        self.rates = []
        self.loss_rates_A = []
        self.loss_rates_B = []
        self.loss_rates_C = []
        
        for i in range(2, len(rate_data)):
            try:
                rate = float(rate_data.iloc[i, 0])
                loss_A = float(rate_data.iloc[i, 1])
                loss_B = float(rate_data.iloc[i, 2])
                loss_C = float(rate_data.iloc[i, 3])
                
                self.rates.append(rate)
                self.loss_rates_A.append(loss_A)
                self.loss_rates_B.append(loss_B)
                self.loss_rates_C.append(loss_C)
            except:
                continue
                
        print(f"利率范围: {min(self.rates)*100:.1f}% - {max(self.rates)*100:.1f}%")
        
    def setup_interpolation(self):
        """设置利率-流失率插值函数"""
        self.interp_A = interp1d(self.rates, self.loss_rates_A, kind='linear', 
                                bounds_error=False, fill_value='extrapolate')
        self.interp_B = interp1d(self.rates, self.loss_rates_B, kind='linear', 
                                bounds_error=False, fill_value='extrapolate')
        self.interp_C = interp1d(self.rates, self.loss_rates_C, kind='linear', 
                                bounds_error=False, fill_value='extrapolate')
    
    def get_customer_loss_rate(self, interest_rate, credit_rating):
        """根据利率和信誉评级获取客户流失率"""
        if credit_rating == 'A':
            return max(0, min(1, self.interp_A(interest_rate)))
        elif credit_rating == 'B':
            return max(0, min(1, self.interp_B(interest_rate)))
        elif credit_rating == 'C':
            return max(0, min(1, self.interp_C(interest_rate)))
        else:
            return max(0, min(1, self.interp_B(interest_rate)))  # 默认B级
    
    def evaluate_solution(self, x, weight_revenue=0.5):
        """
        评估解的目标函数（加权组合）
        x: [selection_1, loan_1, rate_1, selection_2, loan_2, rate_2, ...]
        每个企业3个变量：是否选择(0-1), 贷款金额, 利率
        """
        n_enterprises = len(self.t3_data)
        
        # 解析解向量：每个企业3个变量
        selections = x[::3][:n_enterprises]  # 是否选择该企业
        loans = x[1::3][:n_enterprises]      # 贷款金额
        rates = x[2::3][:n_enterprises]      # 利率
        
        # 应用选择掩码
        actual_loans = [loan if sel > 0.5 else 0 for sel, loan in zip(selections, loans)]
        total_loan = sum(actual_loans)
        
        # 预算约束检查
        if total_loan > self.total_budget:
            return 1e10  # 严重惩罚
        
        total_expected_revenue = 0
        total_loss_amount = 0
        
        for i, (loan, rate) in enumerate(zip(actual_loans, rates)):
            if loan > 0:
                # 约束检查
                if loan < self.min_loan or loan > self.max_loan:
                    return 1e10
                if rate < self.min_rate or rate > self.max_rate:
                    return 1e10
                
                enterprise = self.t3_data.iloc[i]
                credit_rating = enterprise.get('继承信誉评级', 'B')
                
                # 计算客户流失率
                loss_rate = self.get_customer_loss_rate(rate, credit_rating)
                
                # 目标1：期望收益
                expected_revenue = loan * rate * (1 - loss_rate)
                total_expected_revenue += expected_revenue
                
                # 目标2：流失损失
                loss_amount = loan * rate * loss_rate
                total_loss_amount += loss_amount
        
        # 如果没有放贷，返回大惩罚
        if total_expected_revenue == 0:
            return 1e10
        
        # 标准化目标函数
        revenue_scale = 1e6  # 百万元
        loss_scale = 1e6
        
        normalized_revenue = total_expected_revenue / revenue_scale
        normalized_loss = total_loss_amount / loss_scale
        
        # 加权目标函数（最小化）
        weight_loss = 1 - weight_revenue
        objective = -weight_revenue * normalized_revenue + weight_loss * normalized_loss
        
        return objective
    
    def optimize_with_weight(self, weight_revenue):
        """使用指定权重进行优化"""
        n_enterprises = len(self.t3_data)
        
        # 决策变量边界：每个企业3个变量
        bounds = []
        
        for i in range(n_enterprises):
            bounds.append((0, 1))                    # 是否选择 (0-1)
            bounds.append((self.min_loan, self.max_loan))  # 贷款金额
            bounds.append((self.min_rate, self.max_rate))  # 利率
        
        # 使用差分进化算法
        result = differential_evolution(
            lambda x: self.evaluate_solution(x, weight_revenue),
            bounds,
            maxiter=100,
            popsize=20,
            seed=42,
            atol=1e-6,
            tol=1e-6
        )
        
        return result
    
    def analyze_solution(self, x, solution_name):
        """分析解决方案"""
        n_enterprises = len(self.t3_data)
        selections = x[::3][:n_enterprises]
        loans = x[1::3][:n_enterprises]
        rates = x[2::3][:n_enterprises]
        
        allocation_details = []
        total_loan = 0
        total_expected_revenue = 0
        total_loss = 0
        
        for i, (sel, loan, rate) in enumerate(zip(selections, loans, rates)):
            if sel > 0.5 and loan > self.min_loan * 0.1:  # 被选中且有意义的贷款
                enterprise = self.t3_data.iloc[i]
                credit_rating = enterprise.get('继承信誉评级', 'B')
                loss_rate = self.get_customer_loss_rate(rate, credit_rating)
                
                expected_revenue = loan * rate * (1 - loss_rate)
                loss_amount = loan * rate * loss_rate
                
                allocation_details.append({
                    '企业代号': enterprise['企业代号'],
                    'AHP排名': enterprise.get('T3_AHP排名', 'N/A'),
                    'AHP得分': enterprise.get('T3_AHP综合得分', 'N/A'),
                    '抗风险指数': enterprise.get('抗风险指数', 'N/A'),
                    '信誉评级': credit_rating,
                    '分配金额(万元)': loan / 1e4,
                    '贷款利率(%)': rate * 100,
                    '客户流失率(%)': loss_rate * 100,
                    '期望收益(万元)': expected_revenue / 1e4,
                    '流失损失(万元)': loss_amount / 1e4
                })
                
                total_loan += loan
                total_expected_revenue += expected_revenue
                total_loss += loss_amount
        
        # 按分配金额排序
        allocation_details.sort(key=lambda x: x['分配金额(万元)'], reverse=True)
        
        # 统计信息
        stats = {
            '方案名称': solution_name,
            '总放贷额(万元)': total_loan / 1e4,
            '期望年收益(万元)': total_expected_revenue / 1e4,
            '流失损失(万元)': total_loss / 1e4,
            '净收益(万元)': (total_expected_revenue - total_loss) / 1e4,
            '收益率(%)': (total_expected_revenue / total_loan * 100) if total_loan > 0 else 0,
            '损失率(%)': (total_loss / total_loan * 100) if total_loan > 0 else 0,
            '放贷企业数': len(allocation_details),
            '预算利用率(%)': (total_loan / self.total_budget * 100)
        }
        
        return allocation_details, stats

def run_multi_weight_optimization():
    """运行多权重优化"""
    optimizer = T3CorrectedOptimizer()
    
    # 测试不同权重组合
    weight_combinations = [
        (0.8, "收益导向"),
        (0.6, "偏收益"),
        (0.5, "均衡"),
        (0.4, "偏风险控制"),
        (0.2, "风险控制导向")
    ]
    
    results = []
    all_solutions = []
    
    print("\n开始多权重优化...")
    
    for weight_revenue, name in weight_combinations:
        print(f"\n优化 {name} (收益权重: {weight_revenue})")
        
        result = optimizer.optimize_with_weight(weight_revenue)
        
        if result.success:
            allocation_details, stats = optimizer.analyze_solution(result.x, name)
            
            results.append(stats)
            all_solutions.append({
                'name': name,
                'weight': weight_revenue,
                'solution': result.x,
                'details': allocation_details,
                'stats': stats
            })
            
            print(f"✅ {name}优化完成")
            print(f"   期望收益: {stats['期望年收益(万元)']:.2f}万元")
            print(f"   流失损失: {stats['流失损失(万元)']:.2f}万元")
            print(f"   净收益: {stats['净收益(万元)']:.2f}万元")
            print(f"   放贷企业: {stats['放贷企业数']}家")
            print(f"   预算利用率: {stats['预算利用率(%)']:.1f}%")
        else:
            print(f"❌ {name}优化失败: {result.message}")
    
    return results, all_solutions

def create_results_visualization(results, all_solutions):
    """创建结果可视化"""
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 提取数据
    names = [r['方案名称'] for r in results]
    revenues = [r['期望年收益(万元)'] for r in results]
    losses = [r['流失损失(万元)'] for r in results]
    net_revenues = [r['净收益(万元)'] for r in results]
    enterprise_counts = [r['放贷企业数'] for r in results]
    
    colors = ['red', 'orange', 'green', 'blue', 'purple']
    
    # 1. 收益vs损失散点图（Pareto前沿）
    ax1.scatter(losses, revenues, c=colors, s=100, alpha=0.7)
    for i, name in enumerate(names):
        ax1.annotate(name, (losses[i], revenues[i]), xytext=(5, 5), 
                    textcoords='offset points', fontsize=9)
    ax1.set_xlabel('流失损失 (万元)')
    ax1.set_ylabel('期望收益 (万元)')
    ax1.set_title('T3双目标优化：期望收益 vs 流失损失（Pareto前沿）')
    ax1.grid(True, alpha=0.3)
    
    # 2. 净收益对比
    bars1 = ax2.bar(range(len(names)), net_revenues, color=colors, alpha=0.7)
    ax2.set_xlabel('优化方案')
    ax2.set_ylabel('净收益 (万元)')
    ax2.set_title('不同权重方案的净收益对比')
    ax2.set_xticks(range(len(names)))
    ax2.set_xticklabels(names, rotation=45)
    ax2.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, value in zip(bars1, net_revenues):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(net_revenues)*0.01,
                f'{value:.1f}', ha='center', va='bottom', fontsize=8)
    
    # 3. 放贷企业数对比
    bars2 = ax3.bar(range(len(names)), enterprise_counts, color=colors, alpha=0.7)
    ax3.set_xlabel('优化方案')
    ax3.set_ylabel('放贷企业数')
    ax3.set_title('不同权重方案的放贷企业数对比')
    ax3.set_xticks(range(len(names)))
    ax3.set_xticklabels(names, rotation=45)
    ax3.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, value in zip(bars2, enterprise_counts):
        ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                f'{value}', ha='center', va='bottom', fontsize=8)
    
    # 4. 收益率vs损失率
    revenue_rates = [r['收益率(%)'] for r in results]
    loss_rates = [r['损失率(%)'] for r in results]
    
    ax4.scatter(loss_rates, revenue_rates, c=colors, s=100, alpha=0.7)
    for i, name in enumerate(names):
        ax4.annotate(name, (loss_rates[i], revenue_rates[i]), xytext=(5, 5),
                    textcoords='offset points', fontsize=9)
    ax4.set_xlabel('损失率 (%)')
    ax4.set_ylabel('收益率 (%)')
    ax4.set_title('收益率 vs 损失率')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('T3_双目标优化结果.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 结果可视化完成，保存至: T3_双目标优化结果.png")

if __name__ == "__main__":
    # 运行优化
    results, all_solutions = run_multi_weight_optimization()
    
    if results:
        # 保存结果
        results_df = pd.DataFrame(results)
        results_df.to_csv('T3_双目标优化结果汇总.csv', index=False, encoding='utf-8-sig')
        
        # 保存详细分配方案
        for solution in all_solutions:
            if solution['details']:
                details_df = pd.DataFrame(solution['details'])
                filename = f"T3_{solution['name']}_详细分配方案.csv"
                details_df.to_csv(filename, index=False, encoding='utf-8-sig')
        
        # 创建可视化
        create_results_visualization(results, all_solutions)
        
        print("\n" + "="*80)
        print("🎉 T3企业双目标信贷分配优化完成！")
        print("📊 生成文件:")
        print("  - T3_双目标优化结果汇总.csv")
        print("  - T3_*_详细分配方案.csv")
        print("  - T3_双目标优化结果.png")
        print("="*80)
    else:
        print("❌ 优化失败，请检查数据和参数设置")
