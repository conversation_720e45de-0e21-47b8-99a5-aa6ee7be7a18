import pandas as pd

# 读取数据
data = pd.read_csv('T3_权重敏感性分析结果.csv')

print("权重组合效果分析:")
print("="*70)

for _, row in data.iterrows():
    weight = row['权重组合(收益:客户保留)']
    revenue = row['年收益(万元)']
    loss_rate = row['平均客户流失率(%)'] / 100
    profit_rate = row['收益率(%)']
    
    # 计算有效收益率 = 名义收益率 × (1-流失率)
    effective_rate = profit_rate * (1 - loss_rate)
    
    print(f"权重 {weight}:")
    print(f"  名义收益率: {profit_rate:.3f}%")
    print(f"  流失率: {loss_rate*100:.1f}%")
    print(f"  有效收益率: {effective_rate:.3f}%")
    print(f"  实际收益: {revenue:.1f}万元")
    print()
