#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度分析：为什么一些看起来条件不错的企业没有获得贷款？
"""
import pandas as pd
import numpy as np

def detailed_selection_analysis():
    print('='*80)
    print('深度分析：为什么一些看起来条件不错的企业没有获得贷款？')
    print('='*80)
    
    # 读取数据
    all_enterprises = pd.read_csv('T2/T2企业_最终完整数据.csv')
    allocated_enterprises = pd.read_csv('T2/T2_推荐信贷分配方案.csv')
    
    # 分析未获得贷款的企业
    allocated_codes = set(allocated_enterprises['企业代号'])
    not_allocated = all_enterprises[~all_enterprises['企业代号'].isin(allocated_codes)]
    
    print(f'\n🔍 重点分析：高相似度但未获得贷款的企业')
    
    # 找出相似度高于平均水平但未获得贷款的企业
    avg_similarity_allocated = allocated_enterprises['相似度得分'].mean()
    high_similarity_not_allocated = not_allocated[not_allocated['相似度得分'] > avg_similarity_allocated]
    
    print(f'   - 获得贷款企业的平均相似度: {avg_similarity_allocated:.4f}')
    print(f'   - 高相似度但未获得贷款的企业: {len(high_similarity_not_allocated)}家')
    
    if len(high_similarity_not_allocated) > 0:
        print(f'\\n   📋 高相似度未获贷企业详情（前15家）:')
        sample = high_similarity_not_allocated.nlargest(15, '相似度得分')[
            ['企业代号', '继承信誉评级', '相似度得分']
        ]
        for _, row in sample.iterrows():
            print(f'     - {row["企业代号"]}: {row["继承信誉评级"]}级, 相似度{row["相似度得分"]:.4f}')
    
    print(f'\\n🎯 多目标优化的权衡分析')
    
    # 模拟计算这些企业的预期收益和风险
    print(f'   为了理解算法的选择逻辑，我们来分析收益-风险权衡：')
    
    # 读取流失率数据
    churn_data = pd.read_csv('T2/4.csv')
    
    # 构建流失率查询函数（简化版）
    def get_estimated_churn_rate(rating, rate=0.06):
        """估算流失率（简化版）"""
        if rating == 'A':
            return 0.15 + (rate - 0.04) * 5  # A级基础流失率15%
        elif rating == 'B':
            return 0.25 + (rate - 0.04) * 6  # B级基础流失率25%
        elif rating == 'C':
            return 0.35 + (rate - 0.04) * 7  # C级基础流失率35%
        else:
            return 0.50  # D级
    
    # 分析为什么某些企业没被选中
    print(f'\\n   💡 算法选择逻辑分析:')
    
    # 计算一些关键指标
    allocated_enterprises['收益风险比'] = (
        allocated_enterprises['预期收益(元)'] / allocated_enterprises['风险损失(元)']
    )
    
    avg_profit_risk_ratio = allocated_enterprises['收益风险比'].mean()
    min_profit_risk_ratio = allocated_enterprises['收益风险比'].min()
    
    print(f'     - 获得贷款企业的平均收益风险比: {avg_profit_risk_ratio:.4f}')
    print(f'     - 获得贷款企业的最低收益风险比: {min_profit_risk_ratio:.4f}')
    
    # 分析预算利用策略
    print(f'\\n💰 预算利用策略分析:')
    
    total_budget = 100_000_000
    used_budget = allocated_enterprises['贷款金额(元)'].sum()
    avg_loan_amount = allocated_enterprises['贷款金额(元)'].mean()
    
    print(f'   - 平均单笔贷款金额: {avg_loan_amount:,.0f}元')
    print(f'   - 预算利用率: {used_budget/total_budget:.1%}')
    
    # 分析为什么不是100%预算利用率
    remaining_budget = total_budget - used_budget
    min_loan = 100_000
    max_additional_loans = remaining_budget // min_loan
    
    print(f'   - 剩余预算: {remaining_budget:,.0f}元')
    print(f'   - 理论上还可以放贷: {max_additional_loans:.0f}家企业（按最小额度）')
    
    print(f'\\n🧮 算法优化目标分析:')
    print(f'   NSGA-II多目标优化算法的选择原则:')
    print(f'   1. Pareto最优：在收益-风险平面上找到非支配解')
    print(f'   2. 多样性保持：保持解的分布均匀性')
    print(f'   3. 约束满足：严格满足预算和额度约束')
    
    # 分析具体的选择标准
    print(f'\\n📊 实际选择标准分析:')
    
    # 按相似度分组分析
    similarity_bins = pd.cut(all_enterprises['相似度得分'], bins=5, labels=['很低', '低', '中', '高', '很高'])
    all_enterprises['相似度分组'] = similarity_bins
    
    selection_rate_by_similarity = []
    for group in ['很低', '低', '中', '高', '很高']:
        group_enterprises = all_enterprises[all_enterprises['相似度分组'] == group]
        if len(group_enterprises) > 0:
            selected_in_group = len([code for code in group_enterprises['企业代号'] if code in allocated_codes])
            rate = selected_in_group / len(group_enterprises) * 100
            selection_rate_by_similarity.append((group, selected_in_group, len(group_enterprises), rate))
            print(f'   - {group}相似度组: {selected_in_group}/{len(group_enterprises)} ({rate:.1f}%)')
    
    # 按信誉评级分析选择率
    print(f'\\n   按信誉评级的选择率:')
    for rating in ['A', 'B', 'C', 'D']:
        rating_enterprises = all_enterprises[all_enterprises['继承信誉评级'] == rating]
        if len(rating_enterprises) > 0:
            selected_rating = len([code for code in rating_enterprises['企业代号'] if code in allocated_codes])
            rate = selected_rating / len(rating_enterprises) * 100
            print(f'     - {rating}级: {selected_rating}/{len(rating_enterprises)} ({rate:.1f}%)')
    
    print(f'\\n🎲 随机性和算法特性:')
    print(f'   遗传算法的特性可能导致某些看似优秀的企业未被选中:')
    print(f'   1. 随机初始化：初始种群的随机性影响最终结果')
    print(f'   2. 交叉变异：遗传操作的随机性')
    print(f'   3. 局部最优：可能陷入局部最优解')
    print(f'   4. 多目标权衡：在多个目标间寻找平衡')
    
    print(f'\\n🔧 为什么不选择所有"好"企业的原因:')
    print(f'   1. 预算约束：总预算有限，无法满足所有企业')
    print(f'   2. 额度约束：每家企业最少10万元，限制了企业数量')
    print(f'   3. 组合优化：不是单纯选择最好的企业，而是选择最优组合')
    print(f'   4. 风险分散：避免过度集中在某类企业')
    print(f'   5. 多目标平衡：在收益和风险间寻找平衡点')
    
    print(f'\\n💡 改进策略建议:')
    print(f'   如果希望覆盖更多企业，可以考虑:')
    print(f'   1. 增加总预算（如增至1.5亿元）')
    print(f'   2. 降低最小贷款额度（如改为5万元）')
    print(f'   3. 采用分层策略（不同评级不同额度）')
    print(f'   4. 多轮优化（分批次进行贷款分配）')
    print(f'   5. 调整目标权重（更重视覆盖率而非纯收益）')
    
    # 展示一个具体的案例分析
    print(f'\\n📝 案例分析：为什么E127（C级，相似度0.8550）没有获得贷款？')
    e127 = all_enterprises[all_enterprises['企业代号'] == 'E127'].iloc[0]
    print(f'   企业E127特征:')
    print(f'   - 信誉评级: {e127["继承信誉评级"]}级')
    print(f'   - 相似度得分: {e127["相似度得分"]:.4f}（很高）')
    print(f'   \\n   可能的原因:')
    print(f'   1. C级信誉评级风险较高，预期流失率高')
    print(f'   2. 虽然相似度高，但收益风险比可能不够优秀')
    print(f'   3. 算法在多目标优化中选择了其他更平衡的组合')
    print(f'   4. 预算分配给了收益风险比更好的企业')
    
    print('\\n' + '='*80)
    print('✅ 深度分析完成！')
    print('核心结论：企业未获得贷款不仅仅因为单一指标不够好，')
    print('而是算法在多目标优化过程中进行的综合权衡结果。')
    print('='*80)

if __name__ == "__main__":
    detailed_selection_analysis()
