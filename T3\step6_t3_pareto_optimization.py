"""
第三题 步骤6：T3企业双目标信贷分配优化（Pareto前沿分析版本）
目标1：最大化期望收益
目标2：最小化流失损失
采用NSGA-II算法生成Pareto前沿，提供多种决策方案
"""
import pandas as pd
import numpy as np
from deap import base, creator, tools
import random
import matplotlib.pyplot as plt
from scipy.interpolate import interp1d
import copy
import warnings
warnings.filterwarnings('ignore')

print("="*80)
print("第三题 步骤6：T3企业双目标信贷分配优化（Pareto前沿分析）")
print("目标1: 最大化期望收益")  
print("目标2: 最小化流失损失")
print("="*80)

class T3ParetoOptimizer:
    def __init__(self):
        # 约束条件
        self.total_budget = 1e8  # 1亿元
        self.min_loan = 1e5      # 10万元
        self.max_loan = 1e6      # 100万元
        self.min_rate = 0.04     # 4%
        self.max_rate = 0.15     # 15%
        
        # 读取数据
        self.load_data()
        self.setup_interpolation()
        
    def load_data(self):
        """加载T3企业AHP评估结果和利率-流失率关系"""
        print("读取T3企业AHP评估结果...")
        self.t3_data = pd.read_csv('T3企业_AHP综合评估结果.csv')
        print(f"T3企业数据: {len(self.t3_data)}家企业")
        
        print("读取利率-客户流失率关系...")
        rate_data = pd.read_csv('4.csv')
        
        # 解析利率数据
        self.rates = []
        self.loss_rates_A = []
        self.loss_rates_B = []
        self.loss_rates_C = []
        
        for i in range(2, len(rate_data)):
            try:
                rate = float(rate_data.iloc[i, 0])
                loss_A = float(rate_data.iloc[i, 1])
                loss_B = float(rate_data.iloc[i, 2])
                loss_C = float(rate_data.iloc[i, 3])
                
                self.rates.append(rate)
                self.loss_rates_A.append(loss_A)
                self.loss_rates_B.append(loss_B)
                self.loss_rates_C.append(loss_C)
            except:
                continue
                
        print(f"利率范围: {min(self.rates)*100:.1f}% - {max(self.rates)*100:.1f}%")
        
    def setup_interpolation(self):
        """设置利率-流失率插值函数"""
        self.interp_A = interp1d(self.rates, self.loss_rates_A, kind='linear', 
                                bounds_error=False, fill_value='extrapolate')
        self.interp_B = interp1d(self.rates, self.loss_rates_B, kind='linear', 
                                bounds_error=False, fill_value='extrapolate')
        self.interp_C = interp1d(self.rates, self.loss_rates_C, kind='linear', 
                                bounds_error=False, fill_value='extrapolate')
    
    def get_customer_loss_rate(self, interest_rate, credit_rating):
        """根据利率和信誉评级获取客户流失率"""
        if credit_rating == 'A':
            return max(0, min(1, self.interp_A(interest_rate)))
        elif credit_rating == 'B':
            return max(0, min(1, self.interp_B(interest_rate)))
        elif credit_rating == 'C':
            return max(0, min(1, self.interp_C(interest_rate)))
        else:
            return max(0, min(1, self.interp_B(interest_rate)))  # 默认B级
    
    def evaluate_solution(self, solution):
        """
        评估解的目标函数
        solution: [loan_amount_1, rate_1, loan_amount_2, rate_2, ...]
        """
        n_enterprises = len(self.t3_data)
        
        # 解析解向量：每个企业2个变量(贷款金额, 利率)
        loans = solution[::2][:n_enterprises]  # 贷款金额
        rates = solution[1::2][:n_enterprises]  # 利率
        
        total_loan = sum(loans)
        
        # 约束检查
        if total_loan > self.total_budget:
            return -1e10, 1e10  # 惩罚超预算
        
        total_expected_revenue = 0  # 期望收益
        total_loss_amount = 0       # 流失损失
        
        for i, (loan, rate) in enumerate(zip(loans, rates)):
            if loan > 0:
                # 约束检查
                if loan < self.min_loan or loan > self.max_loan:
                    return -1e10, 1e10
                if rate < self.min_rate or rate > self.max_rate:
                    return -1e10, 1e10
                
                enterprise = self.t3_data.iloc[i]
                credit_rating = enterprise.get('继承信誉评级', 'B')
                
                # 计算客户流失率
                loss_rate = self.get_customer_loss_rate(rate, credit_rating)
                
                # 目标1：期望收益（考虑流失率）
                expected_revenue = loan * rate * (1 - loss_rate)
                total_expected_revenue += expected_revenue
                
                # 目标2：流失损失
                loss_amount = loan * rate * loss_rate
                total_loss_amount += loss_amount
        
        # 目标函数：最大化期望收益，最小化流失损失
        return total_expected_revenue, total_loss_amount

# 设置DEAP遗传算法框架
creator.create("FitnessMulti", base.Fitness, weights=(1.0, -1.0))  # 最大化收益，最小化损失
creator.create("Individual", list, fitness=creator.FitnessMulti)

def generate_individual(optimizer):
    """生成随机个体"""
    n_enterprises = len(optimizer.t3_data)
    individual = []
    
    # 随机选择10-50家企业进行贷款
    n_selected = random.randint(10, min(50, n_enterprises))
    selected_indices = random.sample(range(n_enterprises), n_selected)
    
    for i in range(n_enterprises):
        if i in selected_indices:
            # 随机分配贷款金额和利率
            loan = random.uniform(optimizer.min_loan, optimizer.max_loan)
            rate = random.uniform(optimizer.min_rate, optimizer.max_rate)
        else:
            loan = 0.0
            rate = optimizer.min_rate
        
        individual.extend([loan, rate])
    
    # 确保不超预算
    total_loan = sum(individual[::2])
    if total_loan > optimizer.total_budget:
        factor = optimizer.total_budget / total_loan
        for i in range(0, len(individual), 2):
            individual[i] *= factor
    
    return creator.Individual(individual)

def mutate_individual(individual, indpb=0.1):
    """变异操作"""
    n_enterprises = len(global_optimizer.t3_data)

    for i in range(n_enterprises):
        loan_idx = i * 2
        rate_idx = i * 2 + 1

        if random.random() < indpb:
            if individual[loan_idx] > 0:
                # 调整现有贷款
                individual[loan_idx] = max(0, min(global_optimizer.max_loan,
                    individual[loan_idx] * random.uniform(0.5, 1.5)))
                individual[rate_idx] = max(global_optimizer.min_rate, min(global_optimizer.max_rate,
                    individual[rate_idx] + random.uniform(-0.01, 0.01)))
            else:
                # 可能新增贷款
                if random.random() < 0.05:
                    individual[loan_idx] = random.uniform(global_optimizer.min_loan, global_optimizer.max_loan)
                    individual[rate_idx] = random.uniform(global_optimizer.min_rate, global_optimizer.max_rate)

    # 确保预算约束
    total_loan = sum(individual[::2])
    if total_loan > global_optimizer.total_budget:
        factor = global_optimizer.total_budget / total_loan
        for i in range(0, len(individual), 2):
            individual[i] *= factor

    return individual,

def crossover_individuals(ind1, ind2):
    """交叉操作"""
    n = len(ind1)
    
    # 两点交叉
    point1 = random.randint(1, n//2)
    point2 = random.randint(n//2, n-1)
    
    ind1[point1:point2], ind2[point1:point2] = ind2[point1:point2], ind1[point1:point2]
    
    return ind1, ind2

# 全局变量用于变异函数
global_optimizer = None

def run_optimization():
    """运行双目标优化"""
    global global_optimizer
    
    # 初始化优化器
    optimizer = T3ParetoOptimizer()
    global_optimizer = optimizer
    
    # 设置工具箱
    toolbox = base.Toolbox()
    toolbox.register("individual", generate_individual, optimizer)
    toolbox.register("population", tools.initRepeat, list, toolbox.individual)
    toolbox.register("evaluate", optimizer.evaluate_solution)
    toolbox.register("mate", crossover_individuals)
    toolbox.register("mutate", mutate_individual)
    toolbox.register("select", tools.selNSGA2)
    
    print("开始T3双目标优化...")
    print(f"企业数量: {len(optimizer.t3_data)}")
    print(f"预算: {optimizer.total_budget/1e8:.0f}亿元")
    
    # 创建初始种群
    population = toolbox.population(n=100)
    
    # 评估初始种群
    fitnesses = toolbox.map(toolbox.evaluate, population)
    for ind, fit in zip(population, fitnesses):
        ind.fitness.values = fit
    
    # 进化参数
    NGEN = 100
    CXPB = 0.7
    MUTPB = 0.3
    
    print(f"开始进化: {NGEN}代, 交叉率{CXPB}, 变异率{MUTPB}")
    
    # 执行NSGA-II算法
    for gen in range(NGEN):
        # 选择父代
        offspring = toolbox.select(population, len(population))
        offspring = [copy.deepcopy(ind) for ind in offspring]

        # 交叉和变异
        for child1, child2 in zip(offspring[::2], offspring[1::2]):
            if random.random() < CXPB:
                toolbox.mate(child1, child2)
                del child1.fitness.values
                del child2.fitness.values

        for mutant in offspring:
            if random.random() < MUTPB:
                toolbox.mutate(mutant)
                del mutant.fitness.values

        # 评估新个体
        invalid_ind = [ind for ind in offspring if not ind.fitness.valid]
        fitnesses = toolbox.map(toolbox.evaluate, invalid_ind)
        for ind, fit in zip(invalid_ind, fitnesses):
            ind.fitness.values = fit

        # 合并父代和子代，选择下一代
        population = toolbox.select(population + offspring, len(population))

        if gen % 10 == 0:
            print(f"第{gen}代完成")

    print("✅ 进化完成！")
    
    return population, optimizer

def analyze_pareto_front(population, optimizer):
    """分析Pareto前沿，提供决策支持"""
    print("\n📊 开始Pareto前沿分析...")

    # 获取Pareto前沿
    pareto_front = tools.selNSGA2(population, len(population))

    # 提取目标值
    pareto_solutions = []
    for individual in pareto_front:
        if individual.fitness.values[0] > 0:  # 过滤无效解
            pareto_solutions.append({
                'individual': individual,
                'expected_revenue': individual.fitness.values[0],
                'loss_amount': individual.fitness.values[1]
            })

    # 按期望收益排序
    pareto_solutions.sort(key=lambda x: x['expected_revenue'], reverse=True)

    print(f"✅ 获得 {len(pareto_solutions)} 个Pareto最优解")

    # 分析关键解
    solutions = {}

    # 1. 最大收益解
    max_revenue_sol = max(pareto_solutions, key=lambda x: x['expected_revenue'])
    solutions['最大收益解'] = max_revenue_sol

    # 2. 最小损失解
    min_loss_sol = min(pareto_solutions, key=lambda x: x['loss_amount'])
    solutions['最小损失解'] = min_loss_sol

    # 3. 均衡解（收益损失比最大）
    for sol in pareto_solutions:
        if sol['loss_amount'] > 0:
            sol['revenue_loss_ratio'] = sol['expected_revenue'] / sol['loss_amount']
        else:
            sol['revenue_loss_ratio'] = float('inf')

    balanced_sol = max(pareto_solutions, key=lambda x: x['revenue_loss_ratio'])
    solutions['均衡解'] = balanced_sol

    # 4. 保守解（损失率最低的前25%中收益最高的）
    sorted_by_loss = sorted(pareto_solutions, key=lambda x: x['loss_amount'])
    conservative_candidates = sorted_by_loss[:max(1, len(sorted_by_loss)//4)]
    conservative_sol = max(conservative_candidates, key=lambda x: x['expected_revenue'])
    solutions['保守解'] = conservative_sol

    # 5. 激进解（收益最高的前25%中损失最低的）
    sorted_by_revenue = sorted(pareto_solutions, key=lambda x: x['expected_revenue'], reverse=True)
    aggressive_candidates = sorted_by_revenue[:max(1, len(sorted_by_revenue)//4)]
    aggressive_sol = min(aggressive_candidates, key=lambda x: x['loss_amount'])
    solutions['激进解'] = aggressive_sol

    return pareto_solutions, solutions

def generate_detailed_analysis(solution_data, optimizer, solution_name):
    """生成详细的解决方案分析"""
    individual = solution_data['individual']
    n_enterprises = len(optimizer.t3_data)

    # 解析解向量
    loans = individual[::2][:n_enterprises]
    rates = individual[1::2][:n_enterprises]

    # 详细分析
    allocation_details = []
    total_loan = 0
    total_expected_revenue = 0
    total_loss = 0

    for i, (loan, rate) in enumerate(zip(loans, rates)):
        if loan > 0:
            enterprise = optimizer.t3_data.iloc[i]
            credit_rating = enterprise.get('继承信誉评级', 'B')
            loss_rate = optimizer.get_customer_loss_rate(rate, credit_rating)

            expected_revenue = loan * rate * (1 - loss_rate)
            loss_amount = loan * rate * loss_rate

            allocation_details.append({
                '企业代号': enterprise['企业代号'],
                'AHP排名': enterprise.get('T3_AHP排名', 'N/A'),
                'AHP得分': enterprise.get('T3_AHP综合得分', 'N/A'),
                '抗风险指数': enterprise.get('抗风险指数', 'N/A'),
                '信誉评级': credit_rating,
                '分配金额(万元)': loan / 1e4,
                '贷款利率(%)': rate * 100,
                '客户流失率(%)': loss_rate * 100,
                '期望收益(万元)': expected_revenue / 1e4,
                '流失损失(万元)': loss_amount / 1e4
            })

            total_loan += loan
            total_expected_revenue += expected_revenue
            total_loss += loss_amount

    # 按分配金额排序
    allocation_details.sort(key=lambda x: x['分配金额(万元)'], reverse=True)

    # 统计信息
    stats = {
        '方案名称': solution_name,
        '总放贷额(万元)': total_loan / 1e4,
        '期望年收益(万元)': total_expected_revenue / 1e4,
        '流失损失(万元)': total_loss / 1e4,
        '净收益(万元)': (total_expected_revenue - total_loss) / 1e4,
        '收益率(%)': (total_expected_revenue / total_loan * 100) if total_loan > 0 else 0,
        '损失率(%)': (total_loss / total_loan * 100) if total_loan > 0 else 0,
        '放贷企业数': len(allocation_details),
        '预算利用率(%)': (total_loan / optimizer.total_budget * 100),
        '平均利率(%)': np.mean([d['贷款利率(%)'] for d in allocation_details]) if allocation_details else 0,
        '平均流失率(%)': np.mean([d['客户流失率(%)'] for d in allocation_details]) if allocation_details else 0
    }

    # 信誉评级分布
    rating_stats = {}
    for rating in ['A', 'B', 'C']:
        rating_enterprises = [d for d in allocation_details if d['信誉评级'] == rating]
        rating_stats[f'{rating}级企业数'] = len(rating_enterprises)
        rating_stats[f'{rating}级金额(万元)'] = sum(d['分配金额(万元)'] for d in rating_enterprises)

    stats.update(rating_stats)

    return allocation_details, stats

def create_pareto_visualization(pareto_solutions, key_solutions):
    """创建Pareto前沿可视化"""
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # 提取数据
    revenues = [sol['expected_revenue']/1e4 for sol in pareto_solutions]
    losses = [sol['loss_amount']/1e4 for sol in pareto_solutions]

    # 1. Pareto前沿图
    ax1.scatter(losses, revenues, c='blue', alpha=0.6, s=30, label='Pareto前沿解')

    # 标注关键解
    colors = ['red', 'green', 'orange', 'purple', 'brown']
    for i, (name, sol_data) in enumerate(key_solutions.items()):
        revenue = sol_data['expected_revenue'] / 1e4
        loss = sol_data['loss_amount'] / 1e4
        ax1.scatter(loss, revenue, c=colors[i], s=100, marker='*', label=name)
        ax1.annotate(name, (loss, revenue), xytext=(5, 5),
                    textcoords='offset points', fontsize=8)

    ax1.set_xlabel('流失损失 (万元)')
    ax1.set_ylabel('期望收益 (万元)')
    ax1.set_title('Pareto前沿：期望收益 vs 流失损失')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 2. 收益损失比分布
    ratios = []
    for sol in pareto_solutions:
        if sol['loss_amount'] > 0:
            ratios.append(sol['expected_revenue'] / sol['loss_amount'])
        else:
            ratios.append(100)  # 设置上限

    ax2.hist(ratios, bins=20, alpha=0.7, color='lightblue', edgecolor='black')
    ax2.set_xlabel('收益损失比')
    ax2.set_ylabel('解的数量')
    ax2.set_title('收益损失比分布')
    ax2.grid(True, alpha=0.3)

    # 3. 关键解对比（收益）
    solution_names = list(key_solutions.keys())
    solution_revenues = [key_solutions[name]['expected_revenue']/1e4 for name in solution_names]

    bars1 = ax3.bar(range(len(solution_names)), solution_revenues,
                   color=['red', 'green', 'orange', 'purple', 'brown'], alpha=0.7)
    ax3.set_xlabel('解决方案')
    ax3.set_ylabel('期望收益 (万元)')
    ax3.set_title('关键解期望收益对比')
    ax3.set_xticks(range(len(solution_names)))
    ax3.set_xticklabels(solution_names, rotation=45)
    ax3.grid(True, alpha=0.3)

    # 添加数值标签
    for bar, revenue in zip(bars1, solution_revenues):
        ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                f'{revenue:.1f}', ha='center', va='bottom', fontsize=8)

    # 4. 关键解对比（损失）
    solution_losses = [key_solutions[name]['loss_amount']/1e4 for name in solution_names]

    bars2 = ax4.bar(range(len(solution_names)), solution_losses,
                   color=['red', 'green', 'orange', 'purple', 'brown'], alpha=0.7)
    ax4.set_xlabel('解决方案')
    ax4.set_ylabel('流失损失 (万元)')
    ax4.set_title('关键解流失损失对比')
    ax4.set_xticks(range(len(solution_names)))
    ax4.set_xticklabels(solution_names, rotation=45)
    ax4.grid(True, alpha=0.3)

    # 添加数值标签
    for bar, loss in zip(bars2, solution_losses):
        ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                f'{loss:.1f}', ha='center', va='bottom', fontsize=8)

    plt.tight_layout()
    plt.savefig('T3_Pareto前沿分析图.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("✅ Pareto前沿可视化完成，保存至: T3_Pareto前沿分析图.png")

def generate_decision_recommendations(key_solutions):
    """生成决策建议"""
    print("\n🎯 决策建议:")
    print("-" * 60)

    recommendations = {
        '疫情严重期（风险优先）': '保守解',
        '疫情缓解期（平衡策略）': '均衡解',
        '正常经营期（收益优先）': '激进解',
        '追求最大收益': '最大收益解',
        '追求最小风险': '最小损失解'
    }

    for scenario, solution_name in recommendations.items():
        if solution_name in key_solutions:
            sol_data = key_solutions[solution_name]
            revenue = sol_data['expected_revenue'] / 1e4
            loss = sol_data['loss_amount'] / 1e4
            net_revenue = revenue - loss

            print(f"📋 {scenario}:")
            print(f"   推荐方案: {solution_name}")
            print(f"   期望收益: {revenue:.2f}万元")
            print(f"   流失损失: {loss:.2f}万元")
            print(f"   净收益: {net_revenue:.2f}万元")
            print(f"   收益损失比: {revenue/loss:.2f}" if loss > 0 else "   收益损失比: ∞")
            print()

if __name__ == "__main__":
    population, optimizer = run_optimization()
    print("\n✅ 优化完成！开始Pareto前沿分析...")

    # Pareto前沿分析
    pareto_solutions, key_solutions = analyze_pareto_front(population, optimizer)

    # 生成关键解的详细分析
    print("\n📋 生成关键解决方案详细分析...")
    all_solutions_data = []

    for solution_name, solution_data in key_solutions.items():
        allocation_details, stats = generate_detailed_analysis(solution_data, optimizer, solution_name)

        # 保存详细分配方案
        allocation_df = pd.DataFrame(allocation_details)
        filename = f'T3_{solution_name}_详细分配方案.csv'
        allocation_df.to_csv(filename, index=False, encoding='utf-8-sig')

        all_solutions_data.append(stats)

        print(f"✅ {solution_name}分析完成，保存至: {filename}")
        print(f"   📊 放贷企业: {stats['放贷企业数']}家")
        print(f"   💰 期望收益: {stats['期望年收益(万元)']:.2f}万元")
        print(f"   ⚠️  流失损失: {stats['流失损失(万元)']:.2f}万元")
        print(f"   📈 净收益: {stats['净收益(万元)']:.2f}万元")

    # 保存所有方案对比
    comparison_df = pd.DataFrame(all_solutions_data)
    comparison_df.to_csv('T3_Pareto最优解分析.csv', index=False, encoding='utf-8-sig')

    # 可视化Pareto前沿
    create_pareto_visualization(pareto_solutions, key_solutions)

    # 生成决策建议
    generate_decision_recommendations(key_solutions)

    print("\n" + "="*80)
    print("🎉 T3企业双目标信贷分配优化（Pareto前沿分析）完成！")
    print("📊 生成文件:")
    print("  - T3_Pareto最优解分析.csv")
    print("  - T3_*_详细分配方案.csv (5个关键解)")
    print("  - T3_Pareto前沿分析图.png")
    print("="*80)

def create_pareto_visualization(pareto_solutions, key_solutions):
    """创建Pareto前沿可视化"""
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # 提取数据
    revenues = [sol['expected_revenue']/1e4 for sol in pareto_solutions]
    losses = [sol['loss_amount']/1e4 for sol in pareto_solutions]

    # 1. Pareto前沿图
    ax1.scatter(losses, revenues, c='blue', alpha=0.6, s=30, label='Pareto前沿解')

    # 标注关键解
    colors = ['red', 'green', 'orange', 'purple', 'brown']
    for i, (name, sol_data) in enumerate(key_solutions.items()):
        revenue = sol_data['expected_revenue'] / 1e4
        loss = sol_data['loss_amount'] / 1e4
        ax1.scatter(loss, revenue, c=colors[i], s=100, marker='*', label=name)
        ax1.annotate(name, (loss, revenue), xytext=(5, 5),
                    textcoords='offset points', fontsize=8)

    ax1.set_xlabel('流失损失 (万元)')
    ax1.set_ylabel('期望收益 (万元)')
    ax1.set_title('Pareto前沿：期望收益 vs 流失损失')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 2. 收益损失比分布
    ratios = []
    for sol in pareto_solutions:
        if sol['loss_amount'] > 0:
            ratios.append(sol['expected_revenue'] / sol['loss_amount'])
        else:
            ratios.append(100)  # 设置上限

    ax2.hist(ratios, bins=20, alpha=0.7, color='lightblue', edgecolor='black')
    ax2.set_xlabel('收益损失比')
    ax2.set_ylabel('解的数量')
    ax2.set_title('收益损失比分布')
    ax2.grid(True, alpha=0.3)

    # 3. 关键解对比（收益）
    solution_names = list(key_solutions.keys())
    solution_revenues = [key_solutions[name]['expected_revenue']/1e4 for name in solution_names]

    bars1 = ax3.bar(range(len(solution_names)), solution_revenues,
                   color=['red', 'green', 'orange', 'purple', 'brown'], alpha=0.7)
    ax3.set_xlabel('解决方案')
    ax3.set_ylabel('期望收益 (万元)')
    ax3.set_title('关键解期望收益对比')
    ax3.set_xticks(range(len(solution_names)))
    ax3.set_xticklabels(solution_names, rotation=45)
    ax3.grid(True, alpha=0.3)

    # 添加数值标签
    for bar, revenue in zip(bars1, solution_revenues):
        ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                f'{revenue:.1f}', ha='center', va='bottom', fontsize=8)

    # 4. 关键解对比（损失）
    solution_losses = [key_solutions[name]['loss_amount']/1e4 for name in solution_names]

    bars2 = ax4.bar(range(len(solution_names)), solution_losses,
                   color=['red', 'green', 'orange', 'purple', 'brown'], alpha=0.7)
    ax4.set_xlabel('解决方案')
    ax4.set_ylabel('流失损失 (万元)')
    ax4.set_title('关键解流失损失对比')
    ax4.set_xticks(range(len(solution_names)))
    ax4.set_xticklabels(solution_names, rotation=45)
    ax4.grid(True, alpha=0.3)

    # 添加数值标签
    for bar, loss in zip(bars2, solution_losses):
        ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                f'{loss:.1f}', ha='center', va='bottom', fontsize=8)

    plt.tight_layout()
    plt.savefig('T3_Pareto前沿分析图.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("✅ Pareto前沿可视化完成，保存至: T3_Pareto前沿分析图.png")

def generate_decision_recommendations(key_solutions):
    """生成决策建议"""
    print("\n🎯 决策建议:")
    print("-" * 60)

    recommendations = {
        '疫情严重期（风险优先）': '保守解',
        '疫情缓解期（平衡策略）': '均衡解',
        '正常经营期（收益优先）': '激进解',
        '追求最大收益': '最大收益解',
        '追求最小风险': '最小损失解'
    }

    for scenario, solution_name in recommendations.items():
        if solution_name in key_solutions:
            sol_data = key_solutions[solution_name]
            revenue = sol_data['expected_revenue'] / 1e4
            loss = sol_data['loss_amount'] / 1e4
            net_revenue = revenue - loss

            print(f"📋 {scenario}:")
            print(f"   推荐方案: {solution_name}")
            print(f"   期望收益: {revenue:.2f}万元")
            print(f"   流失损失: {loss:.2f}万元")
            print(f"   净收益: {net_revenue:.2f}万元")
            print(f"   收益损失比: {revenue/loss:.2f}" if loss > 0 else "   收益损失比: ∞")
            print()
