"""
第三题 步骤1：为T2企业添加负债水平指标
基于继承的信誉评级计算负债水平：A级90分，B级60分，C级30分
然后与原有七项指标合并形成八项指标
"""
import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler

print("="*80)
print("第三题 步骤1：为T2企业添加负债水平指标")
print("="*80)

# 读取T2企业的七项指标数据
t2_seven_indicators = pd.read_csv('../T2/T2企业_七项指标_标准化.csv')
print(f"T2企业七项标准化指标数据: {len(t2_seven_indicators)}家企业")

# 读取T2企业完整数据（包含继承信誉评级）
t2_complete_data = pd.read_csv('../T2/T2企业_最终完整数据.csv')
print(f"T2企业完整数据: {len(t2_complete_data)}家企业")

# 检查数据列名
print("\nT2七项指标列名:")
print(t2_seven_indicators.columns.tolist())

print("\nT2完整数据列名:")
print(t2_complete_data.columns.tolist())

# 提取信誉评级信息
rating_info = t2_complete_data[['企业代号', '继承信誉评级']].copy()
print(f"提取到信誉评级信息: {len(rating_info)}家企业")

# 检查信誉评级分布
print("\n继承信誉评级分布:")
rating_dist = rating_info['继承信誉评级'].value_counts()
for rating, count in rating_dist.items():
    pct = count / len(rating_info) * 100
    print(f"{rating}级: {count:>3}家 ({pct:>5.1f}%)")

# 根据信誉评级计算负债水平（仿照第一题的评分规则）
def calculate_debt_level(rating):
    """根据信誉评级计算负债水平分数"""
    if rating == 'A':
        return 90
    elif rating == 'B':
        return 60
    elif rating == 'C':
        return 30
    elif rating == 'D':
        return 10  # D级企业不放贷，但先计算出来
    else:
        return 60  # 默认值

# 计算负债水平
rating_info['负债水平'] = rating_info['继承信誉评级'].apply(calculate_debt_level)

print(f"\n负债水平计算结果:")
debt_stats = rating_info.groupby('继承信誉评级')['负债水平'].agg(['count', 'mean'])
debt_stats.columns = ['企业数量', '负债水平分数']
print(debt_stats)

# 对负债水平进行标准化（0-1标准化）
scaler = MinMaxScaler()
rating_info['负债水平_标准化'] = scaler.fit_transform(rating_info[['负债水平']])

print(f"\n负债水平标准化后统计:")
print(f"最小值: {rating_info['负债水平_标准化'].min():.4f}")
print(f"最大值: {rating_info['负债水平_标准化'].max():.4f}")
print(f"平均值: {rating_info['负债水平_标准化'].mean():.4f}")

# 合并负债水平到七项指标中
eight_indicators = pd.merge(
    t2_seven_indicators,
    rating_info[['企业代号', '负债水平_标准化']],
    on='企业代号',
    how='inner'
)

# 重命名列
eight_indicators.rename(columns={'负债水平_标准化': '负债水平'}, inplace=True)

print(f"\n✅ 合并后的八项指标:")
print(f"企业数量: {len(eight_indicators)}家")
print("指标列名:")
indicator_cols = [col for col in eight_indicators.columns if col != '企业代号']
for i, col in enumerate(indicator_cols, 1):
    print(f"  {i}. {col}")

# 显示八项指标的统计信息
print(f"\n八项指标统计摘要:")
for col in indicator_cols:
    data = eight_indicators[col]
    print(f"{col:15s}: min={data.min():6.4f}, max={data.max():6.4f}, mean={data.mean():6.4f}")

# 保存八项指标数据
eight_indicators.to_csv('T2企业_八项指标_标准化.csv', index=False, encoding='utf-8-sig')
print(f"\n✅ T2企业八项标准化指标已保存至: T2企业_八项指标_标准化.csv")

# 显示前10家企业的八项指标
print(f"\n前10家企业的八项标准化指标预览:")
pd.set_option('display.precision', 4)
print(eight_indicators.head(10))

# 排除D级企业（如果有的话）
non_d_enterprises = rating_info[rating_info['继承信誉评级'] != 'D']
if len(non_d_enterprises) < len(rating_info):
    print(f"\n⚠️  发现 {len(rating_info) - len(non_d_enterprises)} 家D级企业，将在后续步骤中排除")
    eight_indicators_filtered = eight_indicators[
        eight_indicators['企业代号'].isin(non_d_enterprises['企业代号'])
    ]
    eight_indicators_filtered.to_csv('T2企业_八项指标_标准化_排除D级.csv', index=False, encoding='utf-8-sig')
    print(f"✅ 排除D级企业后的数据已保存至: T2企业_八项指标_标准化_排除D级.csv")
    print(f"   符合条件企业数量: {len(eight_indicators_filtered)}家")
else:
    print(f"✅ 没有D级企业，所有{len(eight_indicators)}家企业都符合放贷条件")

print(f"\n下一步：计算抗风险指数的四项子指标")
