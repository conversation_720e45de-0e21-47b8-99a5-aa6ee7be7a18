"""
TOPSIS方法详细计算过程演示
以T3的Pareto最优解为例，展示完整的TOPSIS计算步骤
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

print("="*80)
print("TOPSIS方法详细计算过程演示")
print("="*80)

class TOPSISCalculator:
    def __init__(self):
        # 读取数据
        self.data = pd.read_csv('T3_Pareto最优解分析.csv')
        print(f"读取到 {len(self.data)} 个方案")
        
    def step_by_step_topsis(self):
        """逐步演示TOPSIS计算过程"""
        
        print("\n📊 TOPSIS计算的6个步骤")
        print("-" * 60)
        
        # 步骤1：构建决策矩阵
        print("\n🔸 步骤1：构建决策矩阵")
        
        # 选择评价指标（越大越好的指标）
        criteria = ['期望年收益(万元)', '净收益(万元)', '收益率(%)', '放贷企业数']
        weights = [0.3, 0.4, 0.2, 0.1]  # 权重
        
        print(f"评价指标：{criteria}")
        print(f"指标权重：{weights}")
        
        # 构建决策矩阵
        decision_matrix = self.data[criteria].values
        alternatives = self.data['方案名称'].values
        
        print(f"\n原始决策矩阵 ({len(alternatives)} × {len(criteria)})：")
        df_matrix = pd.DataFrame(decision_matrix, 
                               index=alternatives, 
                               columns=criteria)
        print(df_matrix.round(2))
        
        # 步骤2：标准化决策矩阵
        print(f"\n🔸 步骤2：标准化决策矩阵")
        print("公式：rᵢⱼ = xᵢⱼ / √(Σₖ₌₁ᵐ xₖⱼ²)")
        
        # 计算每列的平方和
        column_squares = np.sum(decision_matrix**2, axis=0)
        print(f"\n各指标平方和：")
        for i, criterion in enumerate(criteria):
            print(f"  {criterion}: {column_squares[i]:.2f}")
        
        # 计算标准化矩阵
        column_norms = np.sqrt(column_squares)
        normalized_matrix = decision_matrix / column_norms
        
        print(f"\n标准化决策矩阵：")
        df_normalized = pd.DataFrame(normalized_matrix, 
                                   index=alternatives, 
                                   columns=criteria)
        print(df_normalized.round(4))
        
        # 验证标准化：每列平方和应该为1
        print(f"\n验证标准化（每列平方和应为1）：")
        for i, criterion in enumerate(criteria):
            col_sum = np.sum(normalized_matrix[:, i]**2)
            print(f"  {criterion}: {col_sum:.6f}")
        
        # 步骤3：构建加权标准化矩阵
        print(f"\n🔸 步骤3：构建加权标准化矩阵")
        print("公式：vᵢⱼ = wⱼ × rᵢⱼ")
        
        weighted_matrix = normalized_matrix * weights
        
        print(f"\n加权标准化矩阵：")
        df_weighted = pd.DataFrame(weighted_matrix, 
                                 index=alternatives, 
                                 columns=criteria)
        print(df_weighted.round(4))
        
        # 步骤4：确定正理想解和负理想解
        print(f"\n🔸 步骤4：确定正理想解和负理想解")
        
        # 对于效益型指标（越大越好），正理想解取最大值，负理想解取最小值
        positive_ideal = np.max(weighted_matrix, axis=0)
        negative_ideal = np.min(weighted_matrix, axis=0)
        
        print(f"\n正理想解 A⁺：")
        for i, criterion in enumerate(criteria):
            print(f"  {criterion}: {positive_ideal[i]:.4f}")
        
        print(f"\n负理想解 A⁻：")
        for i, criterion in enumerate(criteria):
            print(f"  {negative_ideal[i]:.4f}")
        
        # 步骤5：计算各方案到理想解的距离
        print(f"\n🔸 步骤5：计算各方案到理想解的距离")
        print("正理想解距离公式：D⁺ᵢ = √(Σⱼ₌₁ⁿ (vᵢⱼ - vⱼ⁺)²)")
        print("负理想解距离公式：D⁻ᵢ = √(Σⱼ₌₁ⁿ (vᵢⱼ - vⱼ⁻)²)")
        
        # 计算到正理想解的距离
        pos_distances = np.sqrt(np.sum((weighted_matrix - positive_ideal)**2, axis=1))
        
        # 计算到负理想解的距离
        neg_distances = np.sqrt(np.sum((weighted_matrix - negative_ideal)**2, axis=1))
        
        print(f"\n各方案到理想解的距离：")
        distance_df = pd.DataFrame({
            '方案名称': alternatives,
            '到正理想解距离(D⁺)': pos_distances,
            '到负理想解距离(D⁻)': neg_distances
        })
        print(distance_df.round(4))
        
        # 步骤6：计算相对接近度
        print(f"\n🔸 步骤6：计算相对接近度")
        print("公式：Cᵢ = D⁻ᵢ / (D⁺ᵢ + D⁻ᵢ)")
        print("Cᵢ ∈ [0,1]，越接近1越好")
        
        # 计算相对接近度
        relative_closeness = neg_distances / (pos_distances + neg_distances)
        
        # 创建最终结果
        results_df = pd.DataFrame({
            '方案名称': alternatives,
            '到正理想解距离': pos_distances,
            '到负理想解距离': neg_distances,
            '相对接近度': relative_closeness,
            'TOPSIS排名': range(1, len(alternatives) + 1)
        })
        
        # 按相对接近度排序
        results_df = results_df.sort_values('相对接近度', ascending=False)
        results_df['TOPSIS排名'] = range(1, len(alternatives) + 1)
        
        print(f"\n🏆 TOPSIS最终排名结果：")
        print(results_df.round(4))
        
        # 详细计算过程示例（以第一个方案为例）
        print(f"\n📝 详细计算示例（以{alternatives[0]}为例）：")
        
        example_idx = 0
        print(f"\n1. 加权标准化值：")
        for j, criterion in enumerate(criteria):
            print(f"   {criterion}: {weighted_matrix[example_idx, j]:.4f}")
        
        print(f"\n2. 到正理想解距离计算：")
        pos_diff_squares = []
        for j, criterion in enumerate(criteria):
            diff = weighted_matrix[example_idx, j] - positive_ideal[j]
            diff_square = diff**2
            pos_diff_squares.append(diff_square)
            print(f"   ({weighted_matrix[example_idx, j]:.4f} - {positive_ideal[j]:.4f})² = {diff_square:.6f}")
        
        pos_distance_example = np.sqrt(sum(pos_diff_squares))
        print(f"   D⁺ = √({sum(pos_diff_squares):.6f}) = {pos_distance_example:.4f}")
        
        print(f"\n3. 到负理想解距离计算：")
        neg_diff_squares = []
        for j, criterion in enumerate(criteria):
            diff = weighted_matrix[example_idx, j] - negative_ideal[j]
            diff_square = diff**2
            neg_diff_squares.append(diff_square)
            print(f"   ({weighted_matrix[example_idx, j]:.4f} - {negative_ideal[j]:.4f})² = {diff_square:.6f}")
        
        neg_distance_example = np.sqrt(sum(neg_diff_squares))
        print(f"   D⁻ = √({sum(neg_diff_squares):.6f}) = {neg_distance_example:.4f}")
        
        print(f"\n4. 相对接近度计算：")
        closeness_example = neg_distance_example / (pos_distance_example + neg_distance_example)
        print(f"   C = {neg_distance_example:.4f} / ({pos_distance_example:.4f} + {neg_distance_example:.4f}) = {closeness_example:.4f}")
        
        return results_df
    
    def visualize_topsis_results(self, results_df):
        """可视化TOPSIS结果"""
        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 1. 相对接近度排名
        ax1.barh(results_df['方案名称'], results_df['相对接近度'], 
                color=['gold', 'silver', '#CD7F32', 'lightblue', 'lightgray'])
        ax1.set_xlabel('相对接近度')
        ax1.set_title('TOPSIS相对接近度排名')
        ax1.grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, (name, value) in enumerate(zip(results_df['方案名称'], results_df['相对接近度'])):
            ax1.text(value + 0.01, i, f'{value:.3f}', va='center')
        
        # 2. 距离对比
        x = np.arange(len(results_df))
        width = 0.35
        
        ax2.bar(x - width/2, results_df['到正理想解距离'], width, 
               label='到正理想解距离', color='red', alpha=0.7)
        ax2.bar(x + width/2, results_df['到负理想解距离'], width,
               label='到负理想解距离', color='green', alpha=0.7)
        
        ax2.set_xlabel('方案')
        ax2.set_ylabel('距离')
        ax2.set_title('各方案到理想解的距离对比')
        ax2.set_xticks(x)
        ax2.set_xticklabels(results_df['方案名称'], rotation=45)
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 散点图：正理想解距离 vs 负理想解距离
        ax3.scatter(results_df['到正理想解距离'], results_df['到负理想解距离'], 
                   c=results_df['相对接近度'], cmap='viridis', s=100)
        
        for i, name in enumerate(results_df['方案名称']):
            ax3.annotate(name, 
                        (results_df.iloc[i]['到正理想解距离'], 
                         results_df.iloc[i]['到负理想解距离']),
                        xytext=(5, 5), textcoords='offset points', fontsize=8)
        
        ax3.set_xlabel('到正理想解距离')
        ax3.set_ylabel('到负理想解距离')
        ax3.set_title('理想解距离散点图（颜色表示相对接近度）')
        ax3.grid(True, alpha=0.3)
        
        # 4. 雷达图：显示最优方案的各指标表现
        best_solution = results_df.iloc[0]['方案名称']
        best_data = self.data[self.data['方案名称'] == best_solution].iloc[0]
        
        criteria = ['期望年收益(万元)', '净收益(万元)', '收益率(%)', '放贷企业数']
        values = [best_data[criterion] for criterion in criteria]
        
        # 标准化到0-1范围用于雷达图
        max_values = [self.data[criterion].max() for criterion in criteria]
        normalized_values = [v/max_v for v, max_v in zip(values, max_values)]
        
        angles = np.linspace(0, 2*np.pi, len(criteria), endpoint=False).tolist()
        normalized_values += normalized_values[:1]  # 闭合图形
        angles += angles[:1]
        
        ax4.plot(angles, normalized_values, 'o-', linewidth=2, label=best_solution)
        ax4.fill(angles, normalized_values, alpha=0.25)
        ax4.set_xticks(angles[:-1])
        ax4.set_xticklabels(criteria)
        ax4.set_ylim(0, 1)
        ax4.set_title(f'TOPSIS最优方案：{best_solution}')
        ax4.grid(True)
        
        plt.tight_layout()
        plt.savefig('TOPSIS详细分析结果.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("✅ TOPSIS可视化结果保存至: TOPSIS详细分析结果.png")
    
    def topsis_sensitivity_analysis(self):
        """TOPSIS权重敏感性分析"""
        print(f"\n📊 TOPSIS权重敏感性分析")
        print("-" * 60)
        
        criteria = ['期望年收益(万元)', '净收益(万元)', '收益率(%)', '放贷企业数']
        
        # 测试不同权重组合
        weight_scenarios = {
            '均等权重': [0.25, 0.25, 0.25, 0.25],
            '收益导向': [0.5, 0.3, 0.15, 0.05],
            '净收益导向': [0.2, 0.6, 0.15, 0.05],
            '收益率导向': [0.2, 0.2, 0.5, 0.1],
            '企业数导向': [0.2, 0.2, 0.2, 0.4]
        }
        
        sensitivity_results = {}
        
        for scenario_name, weights in weight_scenarios.items():
            print(f"\n🔸 {scenario_name}权重方案：{weights}")
            
            # 重新计算TOPSIS
            decision_matrix = self.data[criteria].values
            normalized_matrix = decision_matrix / np.sqrt(np.sum(decision_matrix**2, axis=0))
            weighted_matrix = normalized_matrix * weights
            
            positive_ideal = np.max(weighted_matrix, axis=0)
            negative_ideal = np.min(weighted_matrix, axis=0)
            
            pos_distances = np.sqrt(np.sum((weighted_matrix - positive_ideal)**2, axis=1))
            neg_distances = np.sqrt(np.sum((weighted_matrix - negative_ideal)**2, axis=1))
            relative_closeness = neg_distances / (pos_distances + neg_distances)
            
            # 排名
            ranking = np.argsort(-relative_closeness) + 1
            best_solution_idx = np.argmax(relative_closeness)
            best_solution = self.data.iloc[best_solution_idx]['方案名称']
            
            sensitivity_results[scenario_name] = {
                'best_solution': best_solution,
                'ranking': ranking,
                'closeness': relative_closeness
            }
            
            print(f"   最优方案：{best_solution}")
            print(f"   相对接近度：{relative_closeness[best_solution_idx]:.4f}")
        
        # 分析权重敏感性
        print(f"\n📋 权重敏感性分析总结：")
        solution_counts = {}
        for scenario, result in sensitivity_results.items():
            solution = result['best_solution']
            solution_counts[solution] = solution_counts.get(solution, 0) + 1
        
        print(f"各方案被选为最优的次数：")
        for solution, count in sorted(solution_counts.items(), key=lambda x: x[1], reverse=True):
            print(f"  {solution}: {count}次")
        
        return sensitivity_results

if __name__ == "__main__":
    calculator = TOPSISCalculator()
    
    # 执行完整的TOPSIS计算
    results = calculator.step_by_step_topsis()
    
    # 可视化结果
    calculator.visualize_topsis_results(results)
    
    # 权重敏感性分析
    sensitivity = calculator.topsis_sensitivity_analysis()
    
    print(f"\n✅ TOPSIS详细计算分析完成！")
