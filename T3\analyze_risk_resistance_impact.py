"""
分析T3中抗风险指数的影响
对比T2和T3的结果，分析抗风险指数对最终信贷分配的影响
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

print("="*80)
print("T3抗风险指数影响分析")
print("对比T2和T3的结果，分析抗风险指数的作用")
print("="*80)

def load_data():
    """加载T2和T3的相关数据"""
    print("📊 加载数据...")
    
    try:
        # T2数据
        t2_eight_indicators = pd.read_csv('T2企业_八项指标_标准化_排除D级.csv')
        print(f"T2八项指标数据: {len(t2_eight_indicators)}家企业")
        
        # T3数据
        t3_nine_indicators = pd.read_csv('T2企业_最终九项指标_标准化_排除D级.csv')
        print(f"T3九项指标数据: {len(t3_nine_indicators)}家企业")
        
        # T3 AHP结果
        t3_ahp_results = pd.read_csv('T3企业_AHP综合评估结果.csv')
        print(f"T3 AHP评估结果: {len(t3_ahp_results)}家企业")
        
        # 从九项指标数据中提取抗风险指数
        risk_resistance_data = t3_nine_indicators[['企业代号', '抗风险指数']].copy()
        print(f"抗风险指数数据: {len(risk_resistance_data)}家企业")

        return t2_eight_indicators, t3_nine_indicators, t3_ahp_results, risk_resistance_data
        
    except FileNotFoundError as e:
        print(f"❌ 文件未找到: {e}")
        return None, None, None, None

def analyze_risk_resistance_distribution(risk_resistance_data):
    """分析抗风险指数的分布特征"""
    print("\n📈 抗风险指数分布分析...")
    
    risk_values = risk_resistance_data['抗风险指数'].values
    
    stats = {
        '最小值': np.min(risk_values),
        '最大值': np.max(risk_values),
        '平均值': np.mean(risk_values),
        '中位数': np.median(risk_values),
        '标准差': np.std(risk_values),
        '变异系数': np.std(risk_values) / np.mean(risk_values)
    }
    
    print("抗风险指数统计特征:")
    for key, value in stats.items():
        print(f"  {key}: {value:.4f}")
    
    # 分位数分析
    percentiles = [10, 25, 50, 75, 90]
    print(f"\n分位数分析:")
    for p in percentiles:
        value = np.percentile(risk_values, p)
        print(f"  {p}%分位数: {value:.4f}")
    
    return stats

def compare_rankings(t2_data, t3_ahp_results, risk_resistance_data):
    """对比T2和T3的企业排名变化"""
    print("\n🔄 企业排名变化分析...")

    # 合并数据进行对比
    comparison_data = []
    
    for _, t3_row in t3_ahp_results.iterrows():
        company_id = t3_row['企业代号']
        
        # 查找对应的T2数据
        t2_row = t2_data[t2_data['企业代号'] == company_id]
        
        if not t2_row.empty:
            t2_row = t2_row.iloc[0]
            
            # 计算T2的简单综合得分（等权重）
            t2_indicators = ['盈利能力', '现金流稳定性', '企业规模', '经营风险', 
                           '负债水平', '税负压力', '公司市场竞争力', '盈利预测可靠性']
            
            t2_score = 0
            valid_indicators = 0
            for indicator in t2_indicators:
                if indicator in t2_row and pd.notna(t2_row[indicator]):
                    t2_score += t2_row[indicator]
                    valid_indicators += 1
            
            if valid_indicators > 0:
                t2_score = t2_score / valid_indicators
            
            # 从九项指标数据中获取抗风险指数
            risk_data = risk_resistance_data[risk_resistance_data['企业代号'] == company_id]
            risk_index = risk_data['抗风险指数'].iloc[0] if not risk_data.empty else 0

            comparison_data.append({
                '企业代号': company_id,
                'T2综合得分': t2_score,
                'T3_AHP得分': t3_row['T3_AHP综合得分'],
                '抗风险指数': risk_index,
                '信誉评级': t3_row.get('继承信誉评级', 'B')
            })
    
    comparison_df = pd.DataFrame(comparison_data)
    
    # 计算排名
    comparison_df['T2排名'] = comparison_df['T2综合得分'].rank(ascending=False, method='min')
    comparison_df['T3排名'] = comparison_df['T3_AHP得分'].rank(ascending=False, method='min')
    comparison_df['排名变化'] = comparison_df['T2排名'] - comparison_df['T3排名']
    
    # 分析排名变化
    print(f"排名变化统计:")
    print(f"  排名上升企业数: {len(comparison_df[comparison_df['排名变化'] > 0])}家")
    print(f"  排名下降企业数: {len(comparison_df[comparison_df['排名变化'] < 0])}家")
    print(f"  排名不变企业数: {len(comparison_df[comparison_df['排名变化'] == 0])}家")
    
    # 最大排名变化
    max_up = comparison_df['排名变化'].max()
    max_down = comparison_df['排名变化'].min()
    print(f"  最大排名上升: {max_up}位")
    print(f"  最大排名下降: {abs(max_down)}位")
    
    return comparison_df

def analyze_risk_resistance_impact(comparison_df):
    """分析抗风险指数对排名变化的影响"""
    print("\n🎯 抗风险指数影响分析...")
    
    # 按抗风险指数分组
    comparison_df['抗风险等级'] = pd.cut(comparison_df['抗风险指数'], 
                                    bins=5, labels=['很低', '低', '中', '高', '很高'])
    
    # 分组统计排名变化
    impact_analysis = comparison_df.groupby('抗风险等级').agg({
        '排名变化': ['mean', 'std', 'count'],
        'T2综合得分': 'mean',
        'T3_AHP得分': 'mean',
        '抗风险指数': 'mean'
    }).round(4)
    
    print("按抗风险等级分组的排名变化:")
    print(impact_analysis)
    
    # 相关性分析
    correlation_matrix = comparison_df[['抗风险指数', '排名变化', 'T2综合得分', 'T3_AHP得分']].corr()
    print(f"\n相关性分析:")
    print(f"抗风险指数与排名变化的相关系数: {correlation_matrix.loc['抗风险指数', '排名变化']:.4f}")
    print(f"抗风险指数与T3得分的相关系数: {correlation_matrix.loc['抗风险指数', 'T3_AHP得分']:.4f}")
    
    return impact_analysis, correlation_matrix

def identify_beneficiaries_and_losers(comparison_df):
    """识别抗风险指数的受益者和受损者"""
    print("\n🏆 受益者和受损者分析...")
    
    # 排名上升最多的企业（受益者）
    top_beneficiaries = comparison_df.nlargest(10, '排名变化')
    print("排名上升最多的10家企业（受益者）:")
    for _, row in top_beneficiaries.iterrows():
        print(f"  {row['企业代号']}: 上升{row['排名变化']:.0f}位, "
              f"抗风险指数{row['抗风险指数']:.4f}, "
              f"信誉评级{row['信誉评级']}")
    
    # 排名下降最多的企业（受损者）
    top_losers = comparison_df.nsmallest(10, '排名变化')
    print(f"\n排名下降最多的10家企业（受损者）:")
    for _, row in top_losers.iterrows():
        print(f"  {row['企业代号']}: 下降{abs(row['排名变化']):.0f}位, "
              f"抗风险指数{row['抗风险指数']:.4f}, "
              f"信誉评级{row['信誉评级']}")
    
    # 分析受益者和受损者的特征
    beneficiaries_risk = top_beneficiaries['抗风险指数'].mean()
    losers_risk = top_losers['抗风险指数'].mean()
    
    print(f"\n特征对比:")
    print(f"  受益者平均抗风险指数: {beneficiaries_risk:.4f}")
    print(f"  受损者平均抗风险指数: {losers_risk:.4f}")
    print(f"  差异: {beneficiaries_risk - losers_risk:.4f}")
    
    return top_beneficiaries, top_losers

def create_visualization(comparison_df, risk_resistance_data):
    """创建可视化图表"""
    print("\n📊 生成可视化图表...")
    
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. 抗风险指数分布
    ax1.hist(risk_resistance_data['抗风险指数'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    ax1.set_xlabel('抗风险指数')
    ax1.set_ylabel('企业数量')
    ax1.set_title('抗风险指数分布')
    ax1.grid(True, alpha=0.3)
    
    # 2. 抗风险指数 vs 排名变化散点图
    scatter = ax2.scatter(comparison_df['抗风险指数'], comparison_df['排名变化'], 
                         c=comparison_df['T3_AHP得分'], cmap='viridis', alpha=0.6)
    ax2.set_xlabel('抗风险指数')
    ax2.set_ylabel('排名变化（正值=上升）')
    ax2.set_title('抗风险指数 vs 排名变化')
    ax2.grid(True, alpha=0.3)
    ax2.axhline(y=0, color='red', linestyle='--', alpha=0.5)
    plt.colorbar(scatter, ax=ax2, label='T3 AHP得分')
    
    # 3. T2 vs T3得分对比
    ax3.scatter(comparison_df['T2综合得分'], comparison_df['T3_AHP得分'], alpha=0.6)
    ax3.plot([comparison_df['T2综合得分'].min(), comparison_df['T2综合得分'].max()],
             [comparison_df['T2综合得分'].min(), comparison_df['T2综合得分'].max()],
             'r--', alpha=0.5, label='y=x')
    ax3.set_xlabel('T2综合得分')
    ax3.set_ylabel('T3 AHP得分')
    ax3.set_title('T2 vs T3企业得分对比')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 按信誉评级分组的排名变化
    rating_groups = comparison_df.groupby('信誉评级')['排名变化'].mean()
    bars = ax4.bar(rating_groups.index, rating_groups.values, 
                   color=['green', 'orange', 'red'], alpha=0.7)
    ax4.set_xlabel('信誉评级')
    ax4.set_ylabel('平均排名变化')
    ax4.set_title('不同信誉评级的平均排名变化')
    ax4.grid(True, alpha=0.3)
    ax4.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    
    # 添加数值标签
    for bar, value in zip(bars, rating_groups.values):
        ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                f'{value:.1f}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('T3_抗风险指数影响分析.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 可视化图表保存至: T3_抗风险指数影响分析.png")

def main():
    """主函数"""
    # 加载数据
    t2_data, t3_data, t3_ahp_results, risk_resistance_data = load_data()
    
    if t2_data is None:
        print("❌ 数据加载失败，程序退出")
        return
    
    # 分析抗风险指数分布
    risk_stats = analyze_risk_resistance_distribution(risk_resistance_data)
    
    # 对比排名变化
    comparison_df = compare_rankings(t2_data, t3_ahp_results, risk_resistance_data)
    
    # 分析抗风险指数影响
    impact_analysis, correlation_matrix = analyze_risk_resistance_impact(comparison_df)
    
    # 识别受益者和受损者
    beneficiaries, losers = identify_beneficiaries_and_losers(comparison_df)
    
    # 创建可视化
    create_visualization(comparison_df, risk_resistance_data)
    
    # 保存分析结果
    comparison_df.to_csv('T3_企业排名变化分析.csv', index=False, encoding='utf-8-sig')
    
    print(f"\n✅ 分析完成！结果保存至:")
    print(f"  - T3_企业排名变化分析.csv")
    print(f"  - T3_抗风险指数影响分析.png")
    
    # 总结
    print(f"\n📋 总结:")
    print(f"  🎯 抗风险指数的引入显著改变了企业排名")
    print(f"  📈 {len(comparison_df[comparison_df['排名变化'] > 0])}家企业排名上升")
    print(f"  📉 {len(comparison_df[comparison_df['排名变化'] < 0])}家企业排名下降")
    print(f"  🔗 抗风险指数与排名变化相关系数: {correlation_matrix.loc['抗风险指数', '排名变化']:.4f}")

if __name__ == "__main__":
    main()
