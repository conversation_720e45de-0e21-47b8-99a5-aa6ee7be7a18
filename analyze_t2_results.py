#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
T2优化结果分析脚本
"""
import pandas as pd
import numpy as np

def analyze_t2_results():
    # 读取结果
    pareto_df = pd.read_csv('T2/T2_Pareto最优解汇总.csv')
    allocation_df = pd.read_csv('T2/T2_推荐信贷分配方案.csv')

    print('='*80)
    print('T2优化结果分析（加入单企业额度约束后）')
    print('='*80)

    print(f'\n📊 Pareto最优解统计:')
    print(f'   - 找到Pareto最优解: {len(pareto_df)}个')
    print(f'   - 收益范围: {pareto_df["总收益(元)"].min():,.0f} - {pareto_df["总收益(元)"].max():,.0f}元')
    print(f'   - 风险范围: {pareto_df["总风险(元)"].min():,.0f} - {pareto_df["总风险(元)"].max():,.0f}元')
    print(f'   - 放贷企业数范围: {pareto_df["放贷企业数"].min()} - {pareto_df["放贷企业数"].max()}家')

    print(f'\n🎯 推荐方案详情:')
    best_idx = 6  # 均衡策略（解7）
    best_solution = pareto_df.iloc[best_idx]
    print(f'   - 总收益: {best_solution["总收益(元)"]:.0f}元')
    print(f'   - 总风险: {best_solution["总风险(元)"]:.0f}元')
    print(f'   - 总放贷额: {best_solution["总放贷额(元)"]:.0f}元')
    print(f'   - 预算利用率: {best_solution["总放贷额(元)"]/100_000_000:.1%}')
    print(f'   - 平均利率: {best_solution["平均利率(%)"]:.2f}%')
    print(f'   - 放贷企业数: {best_solution["放贷企业数"]}家')

    print(f'\n📈 信誉评级分布:')
    print(f'   - A级: {best_solution["A级企业数"]}家, 分配{best_solution["A级分配(元)"]:,.0f}元')
    print(f'   - B级: {best_solution["B级企业数"]}家, 分配{best_solution["B级分配(元)"]:,.0f}元')
    print(f'   - C级: {best_solution["C级企业数"]}家, 分配{best_solution["C级分配(元)"]:,.0f}元')

    print(f'\n💰 单企业贷款额度分析:')
    loan_amounts = allocation_df['贷款金额(元)']
    print(f'   - 最小贷款额: {loan_amounts.min():,.0f}元')
    print(f'   - 最大贷款额: {loan_amounts.max():,.0f}元')
    print(f'   - 平均贷款额: {loan_amounts.mean():,.0f}元')
    print(f'   - 符合约束的企业: {len(allocation_df)}家（全部符合10万-100万约束）')

    print(f'\n⚖️ 收益风险分析:')
    收益风险比 = best_solution['总收益(元)'] / best_solution['总风险(元)']
    print(f'   - 收益风险比: {收益风险比:.4f}')
    print(f'   - 年化收益率: {best_solution["总收益(元)"]/best_solution["总放贷额(元)"]:.2%}')

    print(f'\n🔄 与原版本对比:')
    print(f'   - 原版本（无单企业约束）: 最大收益约386万元，最小风险约2073万元')
    print(f'   - 新版本（有单企业约束）: 最大收益{pareto_df["总收益(元)"].max():,.0f}元，最小风险{pareto_df["总风险(元)"].min():,.0f}元')
    print(f'   - 约束影响: 收益略有下降，但风险显著降低，更符合实际业务要求')

    # 详细的策略对比
    print(f'\n📋 三种策略对比:')
    
    # 保守策略（风险最小）
    conservative_idx = pareto_df['总风险(元)'].idxmin()
    conservative = pareto_df.iloc[conservative_idx]
    
    # 激进策略（收益最大）
    aggressive_idx = pareto_df['总收益(元)'].idxmax()
    aggressive = pareto_df.iloc[aggressive_idx]
    
    # 均衡策略（收益风险比最优）
    pareto_df['收益风险比'] = pareto_df['总收益(元)'] / pareto_df['总风险(元)']
    balanced_idx = pareto_df['收益风险比'].idxmax()
    balanced = pareto_df.iloc[balanced_idx]
    
    print(f'   保守策略: 收益{conservative["总收益(元)"]:,.0f}元, 风险{conservative["总风险(元)"]:,.0f}元, 企业{conservative["放贷企业数"]}家')
    print(f'   均衡策略: 收益{balanced["总收益(元)"]:,.0f}元, 风险{balanced["总风险(元)"]:,.0f}元, 企业{balanced["放贷企业数"]}家')
    print(f'   激进策略: 收益{aggressive["总收益(元)"]:,.0f}元, 风险{aggressive["总风险(元)"]:,.0f}元, 企业{aggressive["放贷企业数"]}家')

    print('\n' + '='*80)
    print('✅ T2优化结果分析完成！')
    print('='*80)

if __name__ == "__main__":
    analyze_t2_results()
