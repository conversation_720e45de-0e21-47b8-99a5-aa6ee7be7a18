"""
T2 vs T3 结果对比分析报告
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

print("="*80)
print("T2 vs T3 结果对比分析报告")
print("="*80)

# 读取T2和T3结果
print("读取T2结果...")
t2_pareto = pd.read_csv('../T2/T2_Pareto最优解汇总.csv')
t2_allocation = pd.read_csv('../T2/T2_推荐信贷分配方案.csv')

print("读取T3结果...")
t3_pareto = pd.read_csv('T3_Pareto最优解分析.csv')
t3_allocation = pd.read_csv('T3_最优信贷分配方案.csv')

print("\n" + "="*60)
print("📊 T2 vs T3 核心指标对比")
print("="*60)

# T2最优解
t2_best = t2_pareto.iloc[0]
t3_best = t3_pareto.iloc[0]

print(f"指标对比:")
print(f"{'指标':<20} {'T2':<15} {'T3':<15} {'差异'}")
print("-" * 55)
print(f"{'预算利用率':<18} {100:.1f}%{'':<10} {t3_best['总放贷额(万元)']/10000*100:.1f}%{'':<10} -")
print(f"{'年收益(万元)':<16} {t2_best['总收益(元)']/1e4:.1f}{'':<11} {t3_best['年收益(万元)']:.1f}{'':<11} +{(t3_best['年收益(万元)'] - t2_best['总收益(元)']/1e4):.1f}")
print(f"{'收益率(%)':<18} {t2_best['总收益(元)']/1e8*100:.2f}%{'':<9} {t3_best['收益率(%)']:.2f}%{'':<9} +{(t3_best['收益率(%)'] - t2_best['总收益(元)']/1e8*100):.2f}%")
print(f"{'放贷企业数':<16} {int(t2_best['放贷企业数'])}{'':<11} {int(t3_best['放贷企业数'])}{'':<11} {int(t3_best['放贷企业数'] - t2_best['放贷企业数'])}")
print(f"{'平均利率(%)':<16} {t2_best['平均利率(%)']:.2f}%{'':<9} {t3_best['加权平均利率(%)']:.2f}%{'':<9} +{(t3_best['加权平均利率(%)'] - t2_best['平均利率(%)']):.2f}%")

print(f"\n💡 关键发现:")
print(f"1. T3年收益比T2高 {(t3_best['年收益(万元)'] - t2_best['总收益(元)']/1e4):.1f}万元 ({(t3_best['年收益(万元)'] - t2_best['总收益(元)']/1e4)/(t2_best['总收益(元)']/1e4)*100:.1f}%)")
print(f"2. T3平均利率比T2高 {(t3_best['加权平均利率(%)'] - t2_best['平均利率(%)']):.2f}个百分点")
print(f"3. T3放贷企业数比T2少 {int(t2_best['放贷企业数'] - t3_best['放贷企业数'])}家")

print(f"\n📈 利率分布分析:")
print("T2利率分布:")
t2_rates = t2_allocation['贷款利率(%)'].describe()
print(f"  最低: {t2_rates['min']:.2f}%, 最高: {t2_rates['max']:.2f}%")
print(f"  平均: {t2_rates['mean']:.2f}%, 中位数: {t2_rates['50%']:.2f}%")

print("T3利率分布:")
t3_rates = t3_allocation['贷款利率(%)'].describe()
print(f"  最低: {t3_rates['min']:.2f}%, 最高: {t3_rates['max']:.2f}%")
print(f"  平均: {t3_rates['mean']:.2f}%, 中位数: {t3_rates['50%']:.2f}%")

print(f"\n⚠️  问题分析:")
print("1. **利率过高问题**: T3使用了大量13%-14%的高利率")
print("2. **疫情背景矛盾**: 疫情期间应该降息支持企业，但T3利率反而更高")
print("3. **客户流失率风险**: 高利率导致高客户流失率")

# 统计高利率情况
high_rate_t3 = t3_allocation[t3_allocation['贷款利率(%)'] > 10]
print(f"\n📊 T3高利率企业统计:")
print(f"利率>10%的企业数: {len(high_rate_t3)}家 ({len(high_rate_t3)/len(t3_allocation)*100:.1f}%)")
print(f"这些企业的平均流失率: {high_rate_t3['客户流失率(%)'].mean():.1f}%")
print(f"这些企业贡献收益: {high_rate_t3['年收益(万元)'].sum():.1f}万元 ({high_rate_t3['年收益(万元)'].sum()/t3_allocation['年收益(万元)'].sum()*100:.1f}%)")

print(f"\n💊 建议修正方案:")
print("1. **利率上限约束**: T3应设置利率上限8-9%（考虑疫情支持政策）")
print("2. **风险权重调整**: 增加客户流失率的惩罚权重")
print("3. **疫情调整因子**: 整体利率下调0.5-1%")

# 生成可视化对比
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

# 1. 收益对比
categories = ['T2', 'T3']
revenues = [t2_best['总收益(元)']/1e4, t3_best['年收益(万元)']]
ax1.bar(categories, revenues, color=['blue', 'orange'], alpha=0.7)
ax1.set_ylabel('年收益(万元)')
ax1.set_title('T2 vs T3 年收益对比')
for i, v in enumerate(revenues):
    ax1.text(i, v + 10, f'{v:.1f}万', ha='center', va='bottom')

# 2. 利率分布对比
ax2.hist(t2_allocation['贷款利率(%)'], bins=20, alpha=0.7, label='T2', color='blue')
ax2.hist(t3_allocation['贷款利率(%)'], bins=20, alpha=0.7, label='T3', color='orange')
ax2.set_xlabel('贷款利率(%)')
ax2.set_ylabel('企业数量')
ax2.set_title('T2 vs T3 利率分布对比')
ax2.legend()

# 3. 企业信誉分布对比
t2_rating_counts = t2_allocation['信誉评级'].value_counts()
t3_rating_counts = t3_allocation['信誉评级'].value_counts()

ratings = ['A', 'B', 'C']
t2_counts = [t2_rating_counts.get(r, 0) for r in ratings]
t3_counts = [t3_rating_counts.get(r, 0) for r in ratings]

x = np.arange(len(ratings))
width = 0.35

ax3.bar(x - width/2, t2_counts, width, label='T2', color='blue', alpha=0.7)
ax3.bar(x + width/2, t3_counts, width, label='T3', color='orange', alpha=0.7)
ax3.set_xlabel('信誉评级')
ax3.set_ylabel('企业数量')
ax3.set_title('T2 vs T3 信誉评级分布对比')
ax3.set_xticks(x)
ax3.set_xticklabels(ratings)
ax3.legend()

# 4. 流失率 vs 利率关系
if '客户流失率(%)' in t3_allocation.columns:
    ax4.scatter(t3_allocation['贷款利率(%)'], t3_allocation['客户流失率(%)'], 
                alpha=0.6, color='red')
    ax4.set_xlabel('贷款利率(%)')
    ax4.set_ylabel('客户流失率(%)')
    ax4.set_title('T3 利率与客户流失率关系')
else:
    ax4.text(0.5, 0.5, 'T3客户流失率数据', ha='center', va='center', transform=ax4.transAxes)

plt.tight_layout()
plt.savefig('T2_vs_T3_对比分析.png', dpi=300, bbox_inches='tight')

print(f"\n✅ 对比分析完成！")
print(f"📊 可视化图表已保存: T2_vs_T3_对比分析.png")
print(f"📋 需要修正T3的利率策略以符合疫情支持政策")

print("\n" + "="*80)
print("🎯 T3问题解决建议")
print("="*80)
print("1. 重新设置利率约束: 4%-8% (疫情支持政策)")
print("2. 增加客户流失率惩罚权重")
print("3. 预期合理收益范围: 400-600万元")
print("4. 重新运行优化算法")
print("="*80)
