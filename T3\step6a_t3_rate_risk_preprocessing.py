"""
第三题 步骤6：基于T3 AHP结果的利率和风险预处理
根据4.csv中的利率-客户流失率关系，为T3企业计算利率和风险损失率
"""
import pandas as pd
import numpy as np
from scipy.interpolate import interp1d

print("="*80)
print("第三题 步骤6：T3企业利率和风险预处理")
print("="*80)

# 读取利率数据
rate_data = pd.read_csv('4.csv')
print("读取利率-客户流失率关系数据...")
print(rate_data.head())

# 读取T3 AHP结果
t3_results = pd.read_csv('T3企业_AHP综合评估结果.csv')
print(f"\n读取T3 AHP评估结果: {len(t3_results)}家企业")

# 清理利率数据
# 第一行是表头，第二行是列名，从第三行开始是数据
rates = []
loss_rates_A = []
loss_rates_B = []
loss_rates_C = []

for i in range(2, len(rate_data)):
    try:
        rate = float(rate_data.iloc[i, 0])  # 贷款年利率
        loss_A = float(rate_data.iloc[i, 1])  # 信誉评级A客户流失率
        loss_B = float(rate_data.iloc[i, 2])  # 信誉评级B客户流失率  
        loss_C = float(rate_data.iloc[i, 3])  # 信誉评级C客户流失率
        
        rates.append(rate)
        loss_rates_A.append(loss_A)
        loss_rates_B.append(loss_B)
        loss_rates_C.append(loss_C)
    except:
        continue

print(f"\n解析利率数据:")
print(f"利率范围: {min(rates)*100:.1f}% - {max(rates)*100:.1f}%")
print(f"A级客户流失率范围: {min(loss_rates_A)*100:.1f}% - {max(loss_rates_A)*100:.1f}%")
print(f"B级客户流失率范围: {min(loss_rates_B)*100:.1f}% - {max(loss_rates_B)*100:.1f}%")
print(f"C级客户流失率范围: {min(loss_rates_C)*100:.1f}% - {max(loss_rates_C)*100:.1f}%")

# 创建插值函数
interp_A = interp1d(rates, loss_rates_A, kind='linear', bounds_error=False, fill_value='extrapolate')
interp_B = interp1d(rates, loss_rates_B, kind='linear', bounds_error=False, fill_value='extrapolate')
interp_C = interp1d(rates, loss_rates_C, kind='linear', bounds_error=False, fill_value='extrapolate')

def calculate_optimal_interest_rate(ahp_score, credit_rating):
    """
    基于AHP得分和信誉评级计算最优利率
    AHP得分越高，企业质量越好，可以承受更高利率
    """
    # 基于AHP得分确定利率水平
    # AHP得分范围大约是0.3-0.7，映射到利率范围
    min_rate = 0.04  # 4%
    max_rate = 0.15  # 15%
    
    # 得分越高，利率设定越高（优质企业可承受更高利率）
    base_rate = min_rate + (ahp_score - 0.3) / (0.7 - 0.3) * (max_rate - min_rate)
    base_rate = max(min_rate, min(max_rate, base_rate))
    
    # 根据信誉评级进行调整
    rating_adjustments = {'A': 0.0, 'B': 0.005, 'C': 0.01}  # B级+0.5%, C级+1%
    adjustment = rating_adjustments.get(credit_rating, 0.005)
    
    final_rate = base_rate + adjustment
    final_rate = max(min_rate, min(max_rate, final_rate))
    
    return final_rate

def calculate_customer_loss_rate(interest_rate, credit_rating):
    """根据利率和信誉评级计算客户流失率"""
    if credit_rating == 'A':
        return max(0, interp_A(interest_rate))
    elif credit_rating == 'B':
        return max(0, interp_B(interest_rate))
    elif credit_rating == 'C':
        return max(0, interp_C(interest_rate))
    else:
        return max(0, interp_B(interest_rate))  # 默认使用B级

# 为每家企业计算利率和风险损失率
print(f"\n开始计算T3企业利率和风险参数...")

calculated_rates = []
calculated_loss_rates = []

for _, enterprise in t3_results.iterrows():
    ahp_score = enterprise['T3_AHP综合得分']
    credit_rating = enterprise.get('继承信誉评级', 'B')
    
    # 计算最优利率
    optimal_rate = calculate_optimal_interest_rate(ahp_score, credit_rating)
    
    # 计算对应的客户流失率（作为风险损失率）
    loss_rate = calculate_customer_loss_rate(optimal_rate, credit_rating)
    
    calculated_rates.append(optimal_rate * 100)  # 转换为百分比
    calculated_loss_rates.append(loss_rate)

# 添加到结果中
t3_results['利率(%)'] = calculated_rates
t3_results['风险损失率'] = calculated_loss_rates

print(f"\n✅ 计算完成统计:")
print(f"利率范围: {min(calculated_rates):.2f}% - {max(calculated_rates):.2f}%")
print(f"风险损失率范围: {min(calculated_loss_rates):.4f} - {max(calculated_loss_rates):.4f}")

# 按信誉评级分组分析
if '继承信誉评级' in t3_results.columns:
    print(f"\n按信誉评级分组的利率和风险分析:")
    rating_analysis = t3_results.groupby('继承信誉评级').agg({
        '利率(%)': ['count', 'mean', 'min', 'max'],
        '风险损失率': ['mean', 'min', 'max'],
        'T3_AHP综合得分': ['mean']
    }).round(4)
    print(rating_analysis)

# 显示样本结果
print(f"\n前10家企业的利率和风险参数:")
print("排名 | 企业代号 | AHP得分 | 信誉评级 | 利率(%) | 风险损失率")
print("-" * 65)
for idx, row in t3_results.head(10).iterrows():
    print(f"{row['T3_AHP排名']:>3d} | {row['企业代号']:>6s} | {row['T3_AHP综合得分']:>7.5f} | "
          f"{row.get('继承信誉评级', 'N/A'):>6s} | {row['利率(%)']:>6.2f} | {row['风险损失率']:>8.5f}")

# 保存预处理结果
t3_results.to_csv('T3企业_AHP综合评估结果_含利率风险.csv', index=False, encoding='utf-8-sig')
print(f"\n✅ T3企业利率风险预处理结果已保存至: T3企业_AHP综合评估结果_含利率风险.csv")

print(f"\n" + "="*60)
print("🎉 T3企业利率和风险预处理完成!")
print(f"✅ 基于AHP得分计算最优利率")
print(f"✅ 基于利率-流失率关系计算风险损失率")
print(f"✅ 为{len(t3_results)}家企业完成预处理")
print("="*60)
