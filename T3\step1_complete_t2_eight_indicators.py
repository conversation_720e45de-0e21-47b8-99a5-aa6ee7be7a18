"""
第三题步骤1：完善T2企业的八项指标体系
- 基于T2企业的七项指标
- 添加负债水平指标（基于信誉评级：A=90分, B=60分, C=30分）
- 排除D级企业
- 形成完整的八项指标体系
"""
import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler

print("="*80)
print("第三题步骤1：完善T2企业八项指标体系")
print("="*80)

# 读取T2企业的七项指标和信誉评级数据
try:
    t2_seven_indicators = pd.read_csv('../T2/T2企业_七项指标.csv')
    t2_rating_data = pd.read_csv('../T2/T2企业_最终完整数据.csv')
    
    print(f"T2七项指标数据: {len(t2_seven_indicators)}家企业")
    print(f"T2信誉评级数据: {len(t2_rating_data)}家企业")
    
except FileNotFoundError as e:
    print(f"❌ 错误: 找不到T2数据文件 {e}")
    print("请确保T2文件夹中包含必要的数据文件")
    exit(1)

# 合并数据获取信誉评级
t2_complete = pd.merge(
    t2_seven_indicators, 
    t2_rating_data[['企业代号', '继承信誉评级']], 
    on='企业代号', 
    how='inner'
)

# 重命名列以保持一致性
t2_complete.rename(columns={'继承信誉评级': '预测信誉评级'}, inplace=True)

print(f"合并后数据: {len(t2_complete)}家企业")

# 检查信誉评级分布
print("\nT2企业信誉评级分布:")
rating_dist = t2_complete['预测信誉评级'].value_counts().sort_index()
for rating, count in rating_dist.items():
    pct = count / len(t2_complete) * 100
    print(f"  {rating}级: {count:>3}家 ({pct:>5.1f}%)")

# 排除D级企业（如果有）
print(f"\n根据题目要求，排除信誉评级为D的企业:")
d_count = len(t2_complete[t2_complete['预测信誉评级'] == 'D'])
if d_count > 0:
    print(f"发现D级企业: {d_count}家，将被排除")
    t2_eligible = t2_complete[t2_complete['预测信誉评级'] != 'D'].copy().reset_index(drop=True)
else:
    print("未发现D级企业")
    t2_eligible = t2_complete.copy()

print(f"符合放贷条件的T2企业: {len(t2_eligible)}家")

# 根据信誉评级计算负债水平指标
def calculate_debt_level(rating):
    """根据信誉评级计算负债水平分数"""
    rating_scores = {
        'A': 90,  # A级企业负债风险最低，得分最高
        'B': 60,  # B级企业中等负债风险
        'C': 30   # C级企业负债风险最高，得分最低
    }
    return rating_scores.get(rating, 30)  # 默认给C级分数

# 添加负债水平指标
t2_eligible['负债水平'] = t2_eligible['预测信誉评级'].apply(calculate_debt_level)

print(f"\n负债水平指标统计:")
print(f"  最小值: {t2_eligible['负债水平'].min()}")
print(f"  最大值: {t2_eligible['负债水平'].max()}")
print(f"  平均值: {t2_eligible['负债水平'].mean():.1f}")

# 现在有八项指标了
eight_indicators = [
    '盈利能力', '现金流稳定性', '企业规模', '税负压力', 
    '公司市场竞争力', '盈利预测可靠性', '经营风险',
    '负债水平'  # 新添加的第8项指标
]

print(f"\n✅ 完整八项指标体系:")
for i, indicator in enumerate(eight_indicators, 1):
    print(f"  {i}. {indicator}")

# 检查所有指标的统计信息
print(f"\n八项指标统计摘要:")
for indicator in eight_indicators:
    if indicator in t2_eligible.columns:
        data = t2_eligible[indicator]
        print(f"{indicator:12s}: min={data.min():8.3f}, max={data.max():8.3f}, mean={data.mean():8.3f}")

# 保存完善的八项指标数据
output_data = t2_eligible[['企业代号'] + eight_indicators + ['预测信誉评级']].copy()
output_data.to_csv('T2企业_完整八项指标.csv', index=False, encoding='utf-8-sig')

print(f"\n✅ T2企业完整八项指标已保存至: T2企业_完整八项指标.csv")
print(f"   包含企业数量: {len(output_data)}家")
print(f"   指标数量: {len(eight_indicators)}项")

# 显示前10家企业的指标
print(f"\n前10家企业八项指标预览:")
pd.set_option('display.precision', 3)
pd.set_option('display.width', None)
pd.set_option('display.max_columns', None)
print(output_data.head(10))

print(f"\n下一步：计算抗风险指数的四个子指标")
