"""
步骤2：重新计算基础四项指标
只针对符合条件的99家企业重新计算月利润中位数、企业规模、信用评分、稳定性
"""
import pandas as pd
import numpy as np

print("="*80)
print("步骤2：重新计算基础四项指标（仅针对符合条件的99家企业）")
print("="*80)

# 读取符合条件的企业名单
eligible_enterprises = pd.read_csv('符合条件企业名单.csv')
eligible_company_list = eligible_enterprises['企业代号'].tolist()

print(f"处理企业数量: {len(eligible_company_list)}家")
print("企业代号范围:", eligible_company_list[:10], "...", eligible_company_list[-5:])

# 读取发票数据
print("\n读取发票数据...")
data_input = pd.read_csv('2.csv')  # 进项发票
data_output = pd.read_csv('3.csv')  # 销项发票

print(f"进项发票数据: {len(data_input)}条")
print(f"销项发票数据: {len(data_output)}条")

# 只保留符合条件企业的发票数据
data_input_filtered = data_input[data_input['企业代号'].isin(eligible_company_list)]
data_output_filtered = data_output[data_output['企业代号'].isin(eligible_company_list)]

print(f"筛选后进项发票: {len(data_input_filtered)}条")
print(f"筛选后销项发票: {len(data_output_filtered)}条")

print("\n开始计算基础四项指标...")

def calculate_monthly_profit_median(company_list, input_data, output_data):
    """计算月利润中位数"""
    print("  正在计算指标1: 月利润中位数...")
    monthly_profits = {}
    
    for company in company_list:
        # 获取该企业的有效发票数据
        company_input = input_data[(input_data['企业代号'] == company) & 
                                 (input_data['发票状态'] == '有效发票')]
        company_output = output_data[(output_data['企业代号'] == company) & 
                                   (output_data['发票状态'] == '有效发票')]
        
        if len(company_input) == 0 and len(company_output) == 0:
            monthly_profits[company] = 0
            continue
            
        # 按月份计算利润
        if len(company_input) > 0:
            company_input['月份'] = pd.to_datetime(company_input['开票日期']).dt.to_period('M')
        if len(company_output) > 0:
            company_output['月份'] = pd.to_datetime(company_output['开票日期']).dt.to_period('M')
        
        # 计算每月净利润 (销项 - 进项)
        monthly_input = company_input.groupby('月份')['金额'].sum() if len(company_input) > 0 else pd.Series()
        monthly_output = company_output.groupby('月份')['金额'].sum() if len(company_output) > 0 else pd.Series()
        
        # 合并所有月份
        all_months = set()
        if len(monthly_input) > 0:
            all_months.update(monthly_input.index)
        if len(monthly_output) > 0:
            all_months.update(monthly_output.index)
            
        if len(all_months) == 0:
            monthly_profits[company] = 0
            continue
            
        monthly_profit_values = []
        for month in all_months:
            input_amt = monthly_input.get(month, 0)
            output_amt = monthly_output.get(month, 0)
            profit = output_amt - input_amt
            monthly_profit_values.append(profit)
        
        # 计算中位数
        if len(monthly_profit_values) > 0:
            monthly_profits[company] = np.median(monthly_profit_values)
        else:
            monthly_profits[company] = 0
    
    return monthly_profits

def calculate_enterprise_scale(company_list, input_data, output_data):
    """计算企业规模"""
    print("  正在计算指标2: 企业规模...")
    enterprise_scales = {}
    
    for company in company_list:
        # 获取该企业的有效发票数据
        company_input = input_data[(input_data['企业代号'] == company) & 
                                 (input_data['发票状态'] == '有效发票')]
        company_output = output_data[(output_data['企业代号'] == company) & 
                                   (output_data['发票状态'] == '有效发票')]
        
        # 计算总交易额
        total_input = company_input['金额'].sum() if len(company_input) > 0 else 0
        total_output = company_output['金额'].sum() if len(company_output) > 0 else 0
        total_volume = total_input + total_output
        
        # 计算交易笔数
        total_transactions = len(company_input) + len(company_output)
        
        # 企业规模 = 总交易额 + 交易频次权重
        scale = total_volume + total_transactions * 1000  # 给交易笔数一个权重
        enterprise_scales[company] = scale
    
    return enterprise_scales

def calculate_credit_score(company_list, basic_info):
    """计算信用评分"""
    print("  正在计算指标3: 信用评分...")
    credit_scores = {}
    
    # 信誉评级转换为分数
    rating_scores = {'A': 100, 'B': 80, 'C': 60, 'D': 20}  # D级不会出现在符合条件的企业中
    
    for company in company_list:
        company_info = basic_info[basic_info['企业代号'] == company]
        if len(company_info) > 0:
            rating = company_info.iloc[0]['信誉评级']
            is_default = company_info.iloc[0]['是否违约'] == '是'
            
            base_score = rating_scores.get(rating, 60)
            
            # 违约惩罚
            if is_default:
                base_score -= 30
            
            credit_scores[company] = max(0, base_score)
        else:
            credit_scores[company] = 60  # 默认分数
    
    return credit_scores

def calculate_stability(company_list, input_data, output_data):
    """计算稳定性"""
    print("  正在计算指标4: 稳定性...")
    stability_scores = {}
    
    for company in company_list:
        # 获取该企业的有效发票数据
        company_input = input_data[(input_data['企业代号'] == company) & 
                                 (input_data['发票状态'] == '有效发票')]
        company_output = output_data[(output_data['企业代号'] == company) & 
                                   (output_data['发票状态'] == '有效发票')]
        
        if len(company_input) == 0 and len(company_output) == 0:
            stability_scores[company] = 0
            continue
        
        # 按月统计交易金额
        all_data = []
        if len(company_input) > 0:
            company_input['月份'] = pd.to_datetime(company_input['开票日期']).dt.to_period('M')
            monthly_input = company_input.groupby('月份')['金额'].sum()
            all_data.extend(monthly_input.values)
        
        if len(company_output) > 0:
            company_output['月份'] = pd.to_datetime(company_output['开票日期']).dt.to_period('M')
            monthly_output = company_output.groupby('月份')['金额'].sum()
            all_data.extend(monthly_output.values)
        
        if len(all_data) <= 1:
            stability_scores[company] = 0
        else:
            # 使用变异系数衡量稳定性：越小越稳定
            cv = np.std(all_data) / (np.mean(all_data) + 1)
            # 转换为稳定性分数：变异系数越小，稳定性越高
            stability = 1 / (1 + cv)
            stability_scores[company] = stability
    
    return stability_scores

# 计算四项指标
monthly_profits = calculate_monthly_profit_median(eligible_company_list, data_input_filtered, data_output_filtered)
enterprise_scales = calculate_enterprise_scale(eligible_company_list, data_input_filtered, data_output_filtered)
credit_scores = calculate_credit_score(eligible_company_list, eligible_enterprises)
stability_scores = calculate_stability(eligible_company_list, data_input_filtered, data_output_filtered)

print("\n四项指标计算完成！")

# 创建结果数据框
results_df = pd.DataFrame({
    '企业代号': eligible_company_list,
    '月利润中位数': [monthly_profits[c] for c in eligible_company_list],
    '企业规模': [enterprise_scales[c] for c in eligible_company_list],
    '信用评分': [credit_scores[c] for c in eligible_company_list],
    '稳定性': [stability_scores[c] for c in eligible_company_list]
})

# 添加企业基本信息
results_df = results_df.merge(eligible_enterprises[['企业代号', '信誉评级', '是否违约']], on='企业代号', how='left')

print(f"\n基础指标统计摘要:")
print(f"企业数量: {len(results_df)}")
for col in ['月利润中位数', '企业规模', '信用评分', '稳定性']:
    print(f"{col}: 最小值={results_df[col].min():.2f}, 最大值={results_df[col].max():.2f}, 平均值={results_df[col].mean():.2f}")

# 保存结果
results_df.to_csv('符合条件企业_基础四项指标.csv', index=False, encoding='utf-8-sig')
print(f"\n✅ 基础四项指标已保存至: 符合条件企业_基础四项指标.csv")

print(f"\n前10名企业指标预览:")
print("企业代号 | 月利润中位数 | 企业规模 | 信用评分 | 稳定性 | 信誉等级")
print("-" * 70)
for _, row in results_df.head(10).iterrows():
    print(f"{row['企业代号']:>6} | {row['月利润中位数']:>10.0f} | {row['企业规模']:>8.0f} | {row['信用评分']:>6.0f} | {row['稳定性']:>6.3f} | {row['信誉评级']:>6}")
