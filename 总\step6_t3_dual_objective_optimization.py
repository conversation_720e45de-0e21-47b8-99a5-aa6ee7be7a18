"""
第三题 步骤6：T3企业双目标遗传算法信贷分配优化（正确版本）
目标：最大化银行收益，最小化客户流失率
约束：总预算1亿元，单企业10-100万，利率4%-15%
"""
import pandas as pd
import numpy as np
from deap import base, creator, tools, algorithms
import random
import matplotlib.pyplot as plt
from scipy.interpolate import interp1d
import warnings
warnings.filterwarnings('ignore')

print("="*80)
print("第三题 步骤6：T3企业双目标信贷分配优化")
print("目标1: 最大化银行收益")  
print("目标2: 最小化客户流失率")
print("="*80)

class T3CreditOptimizer:
    def __init__(self):
        # 约束条件
        self.total_budget = 1e8  # 1亿元
        self.min_loan = 1e5      # 10万元
        self.max_loan = 1e6      # 100万元
        self.min_rate = 0.04     # 4%
        self.max_rate = 0.15     # 15%
        
        # 读取数据
        self.load_data()
        self.setup_interpolation()
        
    def load_data(self):
        """加载T3企业AHP评估结果和利率-流失率关系"""
        print("读取T3企业AHP评估结果...")
        self.t3_data = pd.read_csv('T3企业_AHP综合评估结果.csv')
        print(f"T3企业数据: {len(self.t3_data)}家企业")
        
        print("读取利率-客户流失率关系...")
        rate_data = pd.read_csv('4.csv')
        
        # 解析利率数据
        self.rates = []
        self.loss_rates_A = []
        self.loss_rates_B = []
        self.loss_rates_C = []
        
        for i in range(2, len(rate_data)):
            try:
                rate = float(rate_data.iloc[i, 0])
                loss_A = float(rate_data.iloc[i, 1])
                loss_B = float(rate_data.iloc[i, 2])
                loss_C = float(rate_data.iloc[i, 3])
                
                self.rates.append(rate)
                self.loss_rates_A.append(loss_A)
                self.loss_rates_B.append(loss_B)
                self.loss_rates_C.append(loss_C)
            except:
                continue
                
        print(f"利率范围: {min(self.rates)*100:.1f}% - {max(self.rates)*100:.1f}%")
        
    def setup_interpolation(self):
        """设置利率-流失率插值函数"""
        self.interp_A = interp1d(self.rates, self.loss_rates_A, kind='linear', 
                                bounds_error=False, fill_value='extrapolate')
        self.interp_B = interp1d(self.rates, self.loss_rates_B, kind='linear', 
                                bounds_error=False, fill_value='extrapolate')
        self.interp_C = interp1d(self.rates, self.loss_rates_C, kind='linear', 
                                bounds_error=False, fill_value='extrapolate')
    
    def get_customer_loss_rate(self, interest_rate, credit_rating):
        """根据利率和信誉评级获取客户流失率"""
        if credit_rating == 'A':
            return max(0, min(1, self.interp_A(interest_rate)))
        elif credit_rating == 'B':
            return max(0, min(1, self.interp_B(interest_rate)))
        elif credit_rating == 'C':
            return max(0, min(1, self.interp_C(interest_rate)))
        else:
            return max(0, min(1, self.interp_B(interest_rate)))  # 默认B级
    
    def evaluate_solution(self, solution):
        """
        评估解的目标函数
        solution: [loan_amount_1, rate_1, loan_amount_2, rate_2, ...]
        """
        n_enterprises = len(self.t3_data)
        
        # 解析解向量：每个企业2个变量(贷款金额, 利率)
        loans = solution[::2][:n_enterprises]  # 贷款金额
        rates = solution[1::2][:n_enterprises]  # 利率
        
        total_loan = sum(loans)
        
        # 约束检查
        if total_loan > self.total_budget:
            return -1e10, 1e10  # 惩罚超预算
        
        total_revenue = 0
        weighted_loss_rate = 0
        active_loans = 0
        
        for i, (loan, rate) in enumerate(zip(loans, rates)):
            if loan > 0:
                # 约束检查
                if loan < self.min_loan or loan > self.max_loan:
                    return -1e10, 1e10
                if rate < self.min_rate or rate > self.max_rate:
                    return -1e10, 1e10
                
                enterprise = self.t3_data.iloc[i]
                credit_rating = enterprise.get('继承信誉评级', 'B')
                
                # 计算收益
                annual_revenue = loan * rate
                total_revenue += annual_revenue
                
                # 计算客户流失率
                loss_rate = self.get_customer_loss_rate(rate, credit_rating)
                weighted_loss_rate += loss_rate * loan
                active_loans += loan
        
        # 加权平均客户流失率
        if active_loans > 0:
            avg_loss_rate = weighted_loss_rate / active_loans
        else:
            avg_loss_rate = 1.0  # 无贷款时流失率为100%
        
        # 目标函数：最大化收益，最小化流失率
        return total_revenue, avg_loss_rate

# 设置DEAP遗传算法框架
creator.create("FitnessMulti", base.Fitness, weights=(1.0, -1.0))  # 最大化收益，最小化流失率
creator.create("Individual", list, fitness=creator.FitnessMulti)

def generate_individual(n_vars, optimizer):
    """生成随机个体"""
    n_enterprises = len(optimizer.t3_data)
    individual = []
    
    # 随机选择10-50家企业进行贷款
    n_selected = random.randint(10, min(50, n_enterprises))
    selected_indices = random.sample(range(n_enterprises), n_selected)
    
    for i in range(n_enterprises):
        if i in selected_indices:
            # 随机分配贷款金额和利率
            loan = random.uniform(optimizer.min_loan, optimizer.max_loan)
            rate = random.uniform(optimizer.min_rate, optimizer.max_rate)
        else:
            loan = 0.0
            rate = optimizer.min_rate
        
        individual.extend([loan, rate])
    
    # 确保不超预算
    total_loan = sum(individual[::2])
    if total_loan > optimizer.total_budget:
        factor = optimizer.total_budget / total_loan
        for i in range(0, len(individual), 2):
            individual[i] *= factor
    
    return creator.Individual(individual)

def mutate_individual(individual, indpb=0.1):
    """变异操作"""
    # 通过全局变量访问optimizer
    global global_optimizer
    n_enterprises = len(global_optimizer.t3_data)
    
    for i in range(n_enterprises):
        loan_idx = i * 2
        rate_idx = i * 2 + 1
        
        if random.random() < indpb:
            if individual[loan_idx] > 0:
                # 调整现有贷款
                individual[loan_idx] = max(0, min(global_optimizer.max_loan,
                    individual[loan_idx] * random.uniform(0.5, 1.5)))
                individual[rate_idx] = max(global_optimizer.min_rate, min(global_optimizer.max_rate,
                    individual[rate_idx] + random.uniform(-0.01, 0.01)))
            else:
                # 可能新增贷款
                if random.random() < 0.05:
                    individual[loan_idx] = random.uniform(global_optimizer.min_loan, global_optimizer.max_loan)
                    individual[rate_idx] = random.uniform(global_optimizer.min_rate, global_optimizer.max_rate)
    
    # 确保预算约束
    total_loan = sum(individual[::2])
    if total_loan > global_optimizer.total_budget:
        factor = global_optimizer.total_budget / total_loan
        for i in range(0, len(individual), 2):
            individual[i] *= factor
    
    return individual,

def crossover_individuals(ind1, ind2):
    """交叉操作"""
    n_vars = len(ind1)
    point = random.randint(2, n_vars-2)
    # 确保交叉点是偶数（保持贷款-利率对的完整性）
    if point % 2 == 1:
        point += 1
    
    ind1[point:], ind2[point:] = ind2[point:], ind1[point:]
    
    return ind1, ind2

# 主程序
if __name__ == "__main__":
    # 初始化优化器
    optimizer = T3CreditOptimizer()
    global_optimizer = optimizer  # 设置全局变量
    n_enterprises = len(optimizer.t3_data)
    n_vars = n_enterprises * 2  # 每个企业2个变量
    
    # 设置遗传算法
    toolbox = base.Toolbox()
    toolbox.register("individual", generate_individual, n_vars, optimizer)
    toolbox.register("population", tools.initRepeat, list, toolbox.individual)
    toolbox.register("evaluate", optimizer.evaluate_solution)
    toolbox.register("mate", crossover_individuals)
    toolbox.register("mutate", mutate_individual)
    toolbox.register("select", tools.selNSGA2)
    
    print("开始T3双目标优化...")
    print(f"企业数量: {n_enterprises}")
    print(f"变量数量: {n_vars}")
    print(f"预算: {optimizer.total_budget/1e8:.0f}亿元")
    
    # 创建初始种群
    population = toolbox.population(n=100)
    
    # 评估初始种群
    fitnesses = toolbox.map(toolbox.evaluate, population)
    for ind, fit in zip(population, fitnesses):
        ind.fitness.values = fit
    
    # 进化参数
    NGEN = 200
    CXPB = 0.7
    MUTPB = 0.3
    
    print(f"开始进化: {NGEN}代, 交叉率{CXPB}, 变异率{MUTPB}")
    
    # 执行NSGA-II算法
    try:
        # 尝试使用eaNSGA2
        population, logbook = algorithms.eaNSGA2(
            population, toolbox, CXPB, MUTPB, NGEN,
            stats=None, verbose=True
        )
    except AttributeError:
        # 如果没有eaNSGA2，使用标准进化算法
        print("使用标准进化算法...")
        for gen in range(NGEN):
            # 选择
            offspring = toolbox.select(population, len(population))
            offspring = list(map(toolbox.clone, offspring))
            
            # 交叉和变异
            for child1, child2 in zip(offspring[::2], offspring[1::2]):
                if random.random() < CXPB:
                    toolbox.mate(child1, child2)
                    del child1.fitness.values
                    del child2.fitness.values
            
            for mutant in offspring:
                if random.random() < MUTPB:
                    toolbox.mutate(mutant)
                    del mutant.fitness.values
            
            # 评估新个体
            invalid_ind = [ind for ind in offspring if not ind.fitness.valid]
            fitnesses = toolbox.map(toolbox.evaluate, invalid_ind)
            for ind, fit in zip(invalid_ind, fitnesses):
                ind.fitness.values = fit
            
            # 更新种群
            population[:] = toolbox.select(population + offspring, len(population))
            
            if (gen + 1) % 25 == 0:
                print(f"  第{gen+1}代完成")
        
        logbook = None
    
    # 获取Pareto前沿
    pareto_front = tools.selNSGA2(population, len(population))
    
    print(f"✅ 优化完成！获得 {len(pareto_front)} 个Pareto最优解")
    
    # 分析Pareto解
    pareto_analysis = []
    
    for i, solution in enumerate(pareto_front):
        n_enterprises = len(optimizer.t3_data)
        loans = solution[::2][:n_enterprises]
        rates = solution[1::2][:n_enterprises]
        
        total_loan = sum(loans)
        total_revenue = solution.fitness.values[0]
        avg_loss_rate = solution.fitness.values[1]
        
        # 统计各级别企业
        active_count = sum(1 for loan in loans if loan > 0)
        
        a_count = sum(1 for i, loan in enumerate(loans) 
                     if loan > 0 and optimizer.t3_data.iloc[i].get('继承信誉评级') == 'A')
        b_count = sum(1 for i, loan in enumerate(loans) 
                     if loan > 0 and optimizer.t3_data.iloc[i].get('继承信誉评级') == 'B')
        c_count = sum(1 for i, loan in enumerate(loans) 
                     if loan > 0 and optimizer.t3_data.iloc[i].get('继承信誉评级') == 'C')
        
        # 计算平均利率
        weighted_rate = 0
        if total_loan > 0:
            weighted_rate = sum(loans[j] * rates[j] for j in range(len(loans)) if loans[j] > 0) / total_loan
        
        pareto_analysis.append({
            '解编号': i + 1,
            '总放贷额(万元)': total_loan / 1e4,
            '年收益(万元)': total_revenue / 1e4,
            '平均客户流失率(%)': avg_loss_rate * 100,
            '放贷企业数': active_count,
            'A级企业数': a_count,
            'B级企业数': b_count,
            'C级企业数': c_count,
            '加权平均利率(%)': weighted_rate * 100,
            '收益率(%)': (total_revenue / total_loan * 100) if total_loan > 0 else 0
        })
    
    # 保存Pareto分析结果
    pareto_df = pd.DataFrame(pareto_analysis)
    pareto_df = pareto_df.sort_values('年收益(万元)', ascending=False)
    pareto_df.to_csv('T3_Pareto最优解分析.csv', index=False, encoding='utf-8-sig')
    
    print("✅ Pareto最优解分析已保存")
    print("\n前5个最优解:")
    print(pareto_df.head())
    
    # 选择推荐方案（收益最高的方案）
    best_idx = pareto_df['年收益(万元)'].idxmax()
    best_solution = pareto_front[best_idx]
    
    # 生成详细分配方案
    loans = best_solution[::2][:n_enterprises]
    rates = best_solution[1::2][:n_enterprises]
    
    allocation_details = []
    for i, (loan, rate) in enumerate(zip(loans, rates)):
        if loan > 0:
            enterprise = optimizer.t3_data.iloc[i]
            credit_rating = enterprise.get('继承信誉评级', 'N/A')
            loss_rate = optimizer.get_customer_loss_rate(rate, credit_rating)
            
            allocation_details.append({
                '企业代号': enterprise['企业代号'],
                'AHP排名': enterprise['T3_AHP排名'],
                'AHP得分': enterprise['T3_AHP综合得分'],
                '信誉评级': credit_rating,
                '分配金额(万元)': loan / 1e4,
                '贷款利率(%)': rate * 100,
                '年收益(万元)': (loan * rate) / 1e4,
                '客户流失率(%)': loss_rate * 100
            })
    
    # 保存详细分配方案
    allocation_df = pd.DataFrame(allocation_details)
    allocation_df = allocation_df.sort_values('分配金额(万元)', ascending=False)
    allocation_df.to_csv('T3_最优信贷分配方案.csv', index=False, encoding='utf-8-sig')
    
    print(f"\n✅ 最优分配方案已保存: T3_最优信贷分配方案.csv")
    
    # 生成可视化图表
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 中文字体
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. Pareto前沿图
    revenues = [sol.fitness.values[0]/1e4 for sol in pareto_front]
    loss_rates = [sol.fitness.values[1]*100 for sol in pareto_front]
    
    ax1.scatter(loss_rates, revenues, c='red', alpha=0.7)
    ax1.set_xlabel('平均客户流失率 (%)')
    ax1.set_ylabel('年收益 (万元)')
    ax1.set_title('T3 Pareto前沿：收益vs客户流失率')
    ax1.grid(True, alpha=0.3)
    
    # 2. 信誉评级分布
    rating_counts = allocation_df['信誉评级'].value_counts()
    ax2.pie(rating_counts.values, labels=rating_counts.index, autopct='%1.1f%%')
    ax2.set_title('最优方案信誉评级分布')
    
    # 3. 贷款金额分布
    ax3.hist(allocation_df['分配金额(万元)'], bins=20, alpha=0.7, color='skyblue')
    ax3.set_xlabel('贷款金额 (万元)')
    ax3.set_ylabel('企业数量')
    ax3.set_title('贷款金额分布')
    ax3.grid(True, alpha=0.3)
    
    # 4. 利率分布
    ax4.hist(allocation_df['贷款利率(%)'], bins=20, alpha=0.7, color='lightgreen')
    ax4.set_xlabel('贷款利率 (%)')
    ax4.set_ylabel('企业数量')
    ax4.set_title('贷款利率分布')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('T3_优化结果分析图.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 输出摘要
    best_solution_data = pareto_df.iloc[best_idx]
    print(f"\n🎉 T3最优信贷分配方案摘要:")
    print(f"📊 总放贷额: {best_solution_data['总放贷额(万元)']:,.2f}万元")
    print(f"💰 预期年收益: {best_solution_data['年收益(万元)']:,.2f}万元")
    print(f"📈 收益率: {best_solution_data['收益率(%)']:.2f}%")
    print(f"⚠️  平均客户流失率: {best_solution_data['平均客户流失率(%)']:.2f}%")
    print(f"🏢 放贷企业数: {int(best_solution_data['放贷企业数'])}家")
    print(f"📊 信誉分布: A级{int(best_solution_data['A级企业数'])}家, B级{int(best_solution_data['B级企业数'])}家, C级{int(best_solution_data['C级企业数'])}家")
    print(f"💳 加权平均利率: {best_solution_data['加权平均利率(%)']:.2f}%")
    
    print("\n" + "="*80)
    print("🎉 T3企业双目标信贷分配优化完成！")
    print("="*80)
