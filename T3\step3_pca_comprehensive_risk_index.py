"""
第三题 步骤3：使用PCA计算综合抗风险指数
对四项抗风险子指标进行主成分分析，提取第一主成分作为综合抗风险指数
然后标准化后与八项指标合并形成九项指标体系
"""
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.decomposition import PCA

print("="*80)
print("第三题 步骤3：使用PCA计算综合抗风险指数")
print("="*80)

# 读取抗风险指数四项子指标
risk_subindicators = pd.read_csv('T2企业_抗风险指数_四项子指标.csv')
print(f"抗风险子指标数据: {len(risk_subindicators)}家企业")

# 读取T2企业八项指标
eight_indicators = pd.read_csv('T2企业_八项指标_标准化.csv')
print(f"八项指标数据: {len(eight_indicators)}家企业")

# 准备PCA分析的数据
risk_cols = ['供应链集中度', '客户多元度', '企业类别抗风险系数', '企业规模']
risk_data = risk_subindicators[risk_cols].copy()

print(f"\n准备进行PCA分析的四项子指标:")
for i, col in enumerate(risk_cols, 1):
    print(f"  {i}. {col}")

# 数据标准化（PCA需要标准化数据）
scaler = StandardScaler()
risk_data_scaled = scaler.fit_transform(risk_data)

print(f"\n标准化后的子指标统计:")
risk_data_scaled_df = pd.DataFrame(risk_data_scaled, columns=risk_cols)
for col in risk_cols:
    data = risk_data_scaled_df[col]
    print(f"{col:20s}: mean={data.mean():7.4f}, std={data.std():7.4f}")

# 执行PCA分析
pca = PCA()
pca_result = pca.fit_transform(risk_data_scaled)

# 分析PCA结果
print(f"\n✅ PCA分析结果:")
print(f"主成分解释方差比:")
for i, ratio in enumerate(pca.explained_variance_ratio_):
    print(f"  第{i+1}主成分: {ratio:.4f} ({ratio*100:.2f}%)")

cumulative_ratio = np.cumsum(pca.explained_variance_ratio_)
print(f"\n累积解释方差比:")
for i, ratio in enumerate(cumulative_ratio):
    print(f"  前{i+1}个主成分: {ratio:.4f} ({ratio*100:.2f}%)")

# 分析主成分载荷
print(f"\n第一主成分的特征向量（载荷）:")
loadings = pca.components_[0]
for i, (col, loading) in enumerate(zip(risk_cols, loadings)):
    print(f"  {col:20s}: {loading:7.4f}")

# 使用第一主成分作为综合抗风险指数
comprehensive_risk_index = pca_result[:, 0]

# 创建抗风险指数数据框
risk_index_df = pd.DataFrame({
    '企业代号': risk_subindicators['企业代号'],
    '抗风险指数_PCA': comprehensive_risk_index
})

print(f"\n综合抗风险指数（PCA第一主成分）统计:")
print(f"最小值: {comprehensive_risk_index.min():8.4f}")
print(f"最大值: {comprehensive_risk_index.max():8.4f}")
print(f"平均值: {comprehensive_risk_index.mean():8.4f}")
print(f"标准差: {comprehensive_risk_index.std():8.4f}")

# 将抗风险指数标准化到[0,1]区间
min_max_scaler = MinMaxScaler()
risk_index_df['抗风险指数'] = min_max_scaler.fit_transform(
    risk_index_df[['抗风险指数_PCA']]
).flatten()

print(f"\n标准化后的抗风险指数统计:")
print(f"最小值: {risk_index_df['抗风险指数'].min():.4f}")
print(f"最大值: {risk_index_df['抗风险指数'].max():.4f}")
print(f"平均值: {risk_index_df['抗风险指数'].mean():.4f}")

# 合并抗风险指数到八项指标中，形成九项指标
nine_indicators = pd.merge(
    eight_indicators,
    risk_index_df[['企业代号', '抗风险指数']],
    on='企业代号',
    how='inner'
)

print(f"\n✅ 九项指标体系构建完成:")
print(f"企业数量: {len(nine_indicators)}家")

# 显示九项指标列名
indicator_cols = [col for col in nine_indicators.columns if col != '企业代号']
print(f"\n完整九项指标:")
for i, col in enumerate(indicator_cols, 1):
    print(f"  {i:2d}. {col}")

# 显示九项指标的统计摘要
print(f"\n九项指标统计摘要:")
for col in indicator_cols:
    data = nine_indicators[col]
    print(f"{col:15s}: min={data.min():6.4f}, max={data.max():6.4f}, mean={data.mean():6.4f}")

# 保存完整的九项指标数据
nine_indicators.to_csv('T2企业_九项指标_标准化.csv', index=False, encoding='utf-8-sig')
print(f"\n✅ T2企业九项标准化指标已保存至: T2企业_九项指标_标准化.csv")

# 同时保存PCA分析详细结果
pca_details = pd.DataFrame({
    '企业代号': risk_subindicators['企业代号'],
    '供应链集中度': risk_subindicators['供应链集中度'],
    '客户多元度': risk_subindicators['客户多元度'], 
    '企业类别抗风险系数': risk_subindicators['企业类别抗风险系数'],
    '企业规模': risk_subindicators['企业规模'],
    '抗风险指数_PCA原值': comprehensive_risk_index,
    '抗风险指数_标准化': risk_index_df['抗风险指数']
})

pca_details.to_csv('T2企业_PCA抗风险指数详细分析.csv', index=False, encoding='utf-8-sig')
print(f"✅ PCA抗风险指数详细分析已保存至: T2企业_PCA抗风险指数详细分析.csv")

# 显示前10家企业的九项指标
print(f"\n前10家企业的九项标准化指标预览:")
pd.set_option('display.precision', 4)
pd.set_option('display.width', None)
pd.set_option('display.max_columns', None)
print(nine_indicators.head(10))

# PCA分析总结
print(f"\n" + "="*60)
print("🎉 PCA抗风险指数计算完成!")
print(f"✅ 第一主成分解释方差比: {pca.explained_variance_ratio_[0]*100:.2f}%")
print(f"✅ 前三主成分累积解释方差比: {cumulative_ratio[2]*100:.2f}%")
print(f"✅ 九项指标体系构建完成: {len(indicator_cols)}项指标")
print("="*60)

print(f"\n下一步：你可以告诉我如何使用这九项指标进行信贷决策建模")
